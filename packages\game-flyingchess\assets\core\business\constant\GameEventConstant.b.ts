/**
 * @describe 游戏事件监听方法
 * <AUTHOR>
 * @date 2023-08-03 18:13:36
 */

import { Vec2 } from 'cc'

export enum GameEventConstant {
    /**选中手牌 */
    SELECT_HAND_CARD = 'GameEventConstant/SELECT_HAND_CARD',
    // 索取时选中手牌
    STEAL_SELECT_HAND_CARD = 'GameEventConstant/STEAL_SELECT_HAND_CARD',
    /**取消选中手牌 */
    CANCEL_SELECT_HAND_CARD = 'GameEventConstant/CANCEL_SELECT_HAND_CARD',
    /**手牌选中状态变化 */
    HAND_CARD_SELECT_STATE_CHANGE = 'GameEventConstant/HAND_CARD_SELECT_STATE_CHANGE',
    /**显示卡牌信息 */
    SHOW_CARD_INFO = 'GameEventConstant/SHOW_CARD_INFO',
    /**隐藏卡牌信息 */
    HIDE_CARC_INFO = 'GameEventConstant/HIDE_CARC_INFO',
    /**选中埋雷位置 */
    SELECT_DROP_MINE_POS = 'GameEventConstant/SELECT_DROP_MINE_POS',
    /**选择玩家 */
    SELECT_PLAYER = 'GameEventConstant/SELECT_PLAYER',
    /**停止玩家计时 */
    STOP_PLAYER_Timer = 'GameEventConstant/STOP_PLAYER_Timer',

    /**侦查响应 */
    SCOUNT_CARD_RESPONSE = 'GameEventConstant/SCOUNT_CARD_RESPONSE',
    /**绕过响应 */
    BYPASS_CARD_RESPONSE = 'GameEventConstant/BYPASS_CARD_RESPONSE',
    /**调头 */
    TURN_CARD_RESPONSE = 'GameEventConstant/TURN_CARD_RESPONSE',
    /**摸底响应 */
    DRAW_BOTTOM_RESPONSE = 'GameEventConstant/DRAW_BOTTOM_RESPONSE',

    /**状态更新(用于处理服务端主动推送过来导致客户端UI界面未主动关闭 (自动出牌/回合更换)) */
    UPDATE_STATE = 'GameEventConstant/UPDATE_STATE',

    /**关闭队友埋雷界面 */
    CLOSE_TEAM_DROP_MINE = 'GameEventConstant/CLOSE_TEAM_DROP_MINE',

    /**更新观战手牌 */
    UPDATE_WATCH_HANDCARD = 'GameEventConstant/UPDATE_WATCH_HANDCARD',

    /**更新玩家状态(玩家状态切换) */
    UPDATE_PLAYER_STATE = 'GameEventConstant/UPDATE_PLAYER_STATE',

    /**关闭通用UI */
    CLOSE_COMMON_UI = 'GameEventConstant/CLOSE_COMMON_UI',

    /**更新关注状态 */
    UPDATE_FOLLOW_STATE = 'GameEventConstant/UPDATE_FOLLOW_STATE',

    /**手牌状态(显示隐藏) */
    HAND_CARD_STATE = 'GameEventConstant/HAND_CARD_STATE',

    /**清除升级UI关闭计时 */
    CLEAR_UPGRADE_UI_CLOSE_TIME_COUNT = 'GameEventConstant/CLEAR_UPGRADE_UI_CLOSE_TIME_COUNT',

    /**游戏结算关闭倒计时 */
    GAME_CLOSE_TIMEOUT = 'GameEventConstant/GAME_CLOSE_TIMEOUT',

    /**按牙选中 */
    SELECTED_TOOTH = 'GameEventConstant/SELECTED_TOOTH',

    /**抽牌(按牙/侦查)倒计时 */
    DRAW_SCOUT_TIME_OUT = 'GameEventConstant/DRAW_SCOUT_TIME_OUT',

    /**标记牙的状态 */
    TAG_TOOTH_STATE = 'GameEventConstant/TAG_TOOTH_STATE',

    /**关闭规则UI */
    CLOSE_RULE_UI = 'GameEventConstant/CLOSE_RULE_UI',

    /**旋转木桶 */
    ROTATE_BARREL = 'GameEventConstant/ROTATE_BARREL',

    /**点击牙齿 */
    CLICK_TOOTH = 'GameEventConstant/CLICK_TOOTH',

    /**木桶位置 */
    BARREL_ZOOM = 'GameEventConstant/BARREL_ZOOM',

    /**设置木桶旋转到中央 */
    SET_BARREL_ROTATE_CENTER = 'GameEventConstant/SET_BARREL_ROTATE_CENTER',

    /**更新手牌布局 */
    UPDATE_HANDCARD_LAYOUT = 'GameEventConstant/UPDATE_HANDCARD_LAYOUT',
    /**更新手牌布局结束 */
    AFTER_UPDATE_HANDCARD_LAYOUT = 'GameEventConstant/AFTER_UPDATE_HANDCARD_LAYOUT',
}
