import { _decorator, But<PERSON>, Component, is<PERSON><PERSON><PERSON>, Node } from 'cc'
import { watchUser, audioEffect } from '@/core/business/hooks/Decorator'
import { cat } from '@/core/manager'
import UILayer from '@/core/manager/gui/layer/UILayer'
const { ccclass, property } = _decorator

export interface BasePlayerOutOverProps {}

@ccclass('BasePlayerOutOver')
export class BasePlayerOutOver<
    T extends BasePlayerOutOverProps = BasePlayerOutOverProps
> extends UILayer<T> {
    @property({ type: Node, tooltip: '观战按钮节点' })
    btn_continue_watching: Node = null!

    protected override onLoad(): void {
        this.btn_continue_watching.on(
            Button.EventType.CLICK,
            this.onContinueWatchingHandler,
            this
        )
    }

    @watchUser()
    @audioEffect()
    protected onContinueWatchingHandler() {
        cat.gui.closeUI(this)

        cat.tracking.game.battleWatch()
    }
}
