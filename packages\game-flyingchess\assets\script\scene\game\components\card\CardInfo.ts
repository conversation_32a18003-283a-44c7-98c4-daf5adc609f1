import { _decorator, Component, Label, Node, Tween, tween, Vec3 } from 'cc'

import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { Card } from '@/pb-generate/server/pirate/v1/card_pb'
import { CardInfoMap, ExcludedCards } from '@/core/business/types/IGame'
import { cat } from '@/core/manager'
import { GameEventConstant } from '@/core/business/constant'

const { ccclass, property } = _decorator

type CardInfoProps = {
    card: Card
}

@ccclass('CardInfo')
export class CardInfo extends BaseComponent<CardInfoProps> {
    @property({ type: Label, tooltip: '卡牌信息文本节点' })
    card_info_label: Label = null!

    /**显示手牌信息动画 */
    showCardInfo(card: Card) {
        // 删除之前的动画
        this.hideCardInfo()
        // 替换文本
        const nonce = card as Exclude<Card, ExcludedCards>
        if (CardInfoMap[nonce]) {
            // 将CardInfo类型转换为string类型
            this.card_info_label.string = CardInfoMap[nonce].toString()
            this.node.active = true
            tween(this.node)
                .to(0.2, { scale: Vec3.ONE })
                .delay(2.0)
                .call(() => {
                    this.hideCardInfo()
                })
                .start()
        }
    }

    /**隐藏手牌信息 */
    hideCardInfo() {
        Tween.stopAllByTarget(this.node)
        this.node.scale = Vec3.ZERO
        this.node.active = false
        cat.event.dispatchEvent(GameEventConstant.HIDE_CARC_INFO)
    }
}
