/**
 * @describe 音效常量
 * <AUTHOR>
 * @date 2023-08-03 10:48:34
 */

export enum AudioEffectConstant {
    /**BGM */
    BGM = 'audio/pirate_bgm',
    /**CLICK */
    CLICK = 'audio/click',
    /**被攻击音效 */
    ATTRACT = 'audio/pirate_attract',
    /**被攻击音效 */
    BEING_ATTRACT = 'audio/pirate_beingattract',
    /**治疗音效 */
    CURE = 'audio/pirate_cure',
    /**死亡音效 */
    DEAD = 'audio/pirate_dead',
    /**发牌音效 */
    DEAL_CARD = 'audio/pirate_dealcard',
    /**虚弱音效 */
    DEBILITATE = 'audio/pirate_debilitate',
    /**防御音效 */
    DEFENSE = 'audio/pirate_defense',
    /**效果触发音效 */
    EFFECT_TRIGGER = 'audio/pirate_effecttrigger',
    /**交换音效 */
    EXCHANGE = 'audio/pirate_exchange',
    /**失败音效 */
    FAILED = 'audio/pirate_failed',
    /**冰冻音效 */
    FROZEN = 'audio/pirate_frozen',
    /**获取卡牌音效 */
    GET_CARD = 'audio/pirate_getcard',
    /**命中弱点音效 */
    HIT_WEAKNESS = 'audio/pirate_hitweakness',
    /**解冻音效 */
    ICE_BROKEN = 'audio/pirate_icebroken',
    /**检查音效 */
    INSPECT = 'audio/pirate_inspect',
    /**海盗请求音效 */
    REQUEST = 'audio/pirate_request',
    /**静音 */
    SILENT = 'audio/silent',
    /**强化音效 */
    STRENGTHEN = 'audio/pirate_strengthen',
    /**成功音效 */
    SUCCESS = 'audio/pirate_success',
    /**转移音效 */
    TRANSFER = 'audio/pirate_transfer',
}
