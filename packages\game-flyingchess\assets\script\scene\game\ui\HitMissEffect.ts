import { _decorator, Component, Node, Vec3, sp, isValid } from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { cat } from '@/core/manager'
import { GameEventConstant } from '@/core/business/constant'

const { ccclass, property } = _decorator

interface HitMissEffectProps {
    // 可以添加需要的属性
}

/**
 * 命中/未命中特效组件
 * 用于在 UI 层显示 Spine 动画
 */
@ccclass('HitMissEffect')
export class HitMissEffect extends BaseComponent<HitMissEffectProps> {
    @property({ type: Node, tooltip: '特效容器节点' })
    effect_container: Node = null!

    @property({ type: sp.Skeleton, tooltip: '命中/未命中 Spine 组件' })
    hitMissSpine: sp.Skeleton = null!

    @property({ tooltip: '特效固定位置' })
    fixedPosition: Vec3 = new Vec3(-160.05, -37.21, 0.91) // y轴向下移动140单位

    protected override initUI(): void {
        // 初始化时隐藏容器
        if (this.effect_container) {
            this.effect_container.active = false
        }
    }

    protected override onEventListener(): void {
        // 监听显示特效事件
        cat.event.on(
            GameEventConstant.SHOW_HIT_MISS_EFFECT,
            this.onShowHitMissEffect,
            this
        )
    }

    override onDestroy(): void {
        // 移除事件监听
        cat.event.off(
            GameEventConstant.SHOW_HIT_MISS_EFFECT,
            this.onShowHitMissEffect,
            this
        )
    }

    /**
     * 显示命中/未命中特效事件处理
     * @param data 事件数据（空对象，不再需要位置信息）
     */
    private onShowHitMissEffect(data: {}): void {
        console.log('[HitMissEffect] 收到显示特效事件:', data)

        // 确保容器节点和 Spine 组件存在
        if (!this.effect_container || !this.hitMissSpine) {
            console.error(
                '[HitMissEffect] effect_container 或 hitMissSpine 不存在'
            )
            return
        }

        // 使用固定位置
        this.effect_container.setPosition(this.fixedPosition)
        console.log(
            '[HitMissEffect] 使用固定位置:',
            this.fixedPosition.toString()
        )

        // 显示容器
        this.effect_container.active = true

        // 播放动画
        this.playHitMissAnimation()
    }

    private playHitMissAnimation(): void {
        if (!this.hitMissSpine || !isValid(this.hitMissSpine.node)) {
            console.error('[HitMissEffect] hitMissSpine 不存在或无效')
            return
        }

        // 设置动画完成回调
        this.hitMissSpine.setCompleteListener(() => {
            if (isValid(this.hitMissSpine)) {
                // 动画播放完成后隐藏节点
                this.effect_container.active = false
                console.log('[HitMissEffect] hit_miss 动画播放完成')
            }
        })

        // 显示节点并播放动画
        this.hitMissSpine.node.active = true
        this.hitMissSpine.setAnimation(0, 'idle', false) // 使用 hit.json 中的 idle 动画
        console.log(
            `[HitMissEffect] 开始播放 hit_miss 动画, isHit: ${'未命中'}`
        )
    }
}
