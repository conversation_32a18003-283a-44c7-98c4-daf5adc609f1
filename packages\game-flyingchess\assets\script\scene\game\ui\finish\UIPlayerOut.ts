import {
    _decorator,
    Button,
    Component,
    is<PERSON><PERSON><PERSON>,
    Label,
    Node,
    Sprite,
    SpriteFrame,
} from 'cc'
import { BasePlayerOutOver } from './BasePlayerOutOver'
import { cat } from '@/core/manager'
// import {
//     ReportGameStateRequest_GameSettlePayload,
//     ReportGameStateRequest_GameSettlePayloadSchema,
// } from '@/pb-generate/server/shark/suileyoo/v1/report_battle_pb'
import { create } from '@bufbuild/protobuf'
import { audioEffect } from '@/core/business/hooks/Decorator'
const { ccclass, property } = _decorator

type PlayerOutProps = ReportGameStateRequest_GameSettlePayload

const RankIndex = ['一', '二', '三', '四', '五', '六']

@ccclass('PlayerOut')
export class UIPlayerOut extends BasePlayerOutOver<PlayerOutProps> {
    @property({ type: Label, tooltip: '名次节点' })
    rank: Label

    @property({ type: Sprite, tooltip: '标题节点' })
    title: Sprite

    @property({ type: Sprite, tooltip: 'logo节点' })
    logo: Sprite

    @property({ type: Button, tooltip: '观战按钮节点' })
    btn_continue: Button

    @property({ type: [SpriteFrame], tooltip: '标题精灵图集' })
    title_spriteFrame: SpriteFrame[] = []

    @property({ type: [SpriteFrame], tooltip: 'logo精灵图集' })
    logo_spriteFrame: SpriteFrame[] = []

    @property({ type: [SpriteFrame], tooltip: '观战按钮精灵图集' })
    btn_continue_spriteFrame: SpriteFrame[] = []

    override props: ReportGameStateRequest_GameSettlePayload = create(
        ReportGameStateRequest_GameSettlePayloadSchema
    )

    protected override start(): void {
        if (this.props.teamSettleResults) {
            const { rank } = this.props.teamSettleResults[0]
            this.rank.string = `第${RankIndex[rank - 1]}名`
            this.logo.spriteFrame = this.logo_spriteFrame[rank === 1 ? 0 : 1]
            this.title.spriteFrame = this.title_spriteFrame[rank === 1 ? 0 : 1]

            this.btn_continue.getComponent(Sprite)!.spriteFrame =
                this.btn_continue_spriteFrame[[1, 2].includes(rank) ? 1 : 0]

            this.rank.node.active = true
        } else {
            this.rank.node.active = false
        }

        // // 观战用户5s自动关闭
        // this.scheduleOnce(() => {
        //     if (isValid(this)) cat.gui.closeUI(this)
        // }, 5)
    }
}
