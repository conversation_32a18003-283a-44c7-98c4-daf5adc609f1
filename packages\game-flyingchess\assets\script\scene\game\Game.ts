import {
    __private,
    _decorator,
    AudioSource,
    Button,
    error,
    ForwardFlow,
    instantiate,
    isValid,
    Label,
    log,
    Node,
    Prefab,
    sp,
    SpriteFrame,
    Tween,
    tween,
    UITransform,
} from 'cc'
import SceneLayer from '@/core/manager/gui/scene/SceneLayer'
import { BattleInitialData, Player as BasePlayer, BattleMode } from 'sgc'
import {
    GameEventConstant,
    GlobalEventConstant,
    AudioEffectConstant,
    RoomEventConstant,
} from '@/core/business/constant'
import JoinBattle from '@/core/business/hooks/JoinBattle'
import store, { Store } from '@/core/business/store'
import { SocketEvent } from '@/core/business/ws'
import { cat } from '@/core/manager'
import { Card } from '@/pb-generate/server/pirate/v1/card_pb'

import {
    GameOverBroadcast,
    GameOverBroadcastSchema,
    SyncResponseSchema,
    SyncResponseJson,
    SyncResponse,
    GameInfoBroadcastSchema,
} from '@/pb-generate/server/pirate/v1/game_pb'

import {
    Player,
    PlayerInfoMessage,
    PlayerAfterSelectTargetBroadcast,
    PlayerAfterSelectTargetBroadcastSchema,
} from '@/pb-generate/server/pirate/v1/player_pb'

import { State as DeskState } from '@/pb-generate/server/pirate/v1/state_pb'
// import {
//     ReportGameStateRequest_GameSettlePayload,
//     ReportGameStateRequest,
// } from '@/pb-generate/server/shark/suileyoo/v1/report_battle_pb'
import { CardInfo } from './components/card/CardInfo'
import { Desk } from './components/desk/Desk'
import { create, fromBinary } from '@bufbuild/protobuf'
import { UIPlayerOut } from './ui/finish/UIPlayerOut'
import { PlayerItem } from './components/player/PlayerItem'
import { Barrel } from './components/barrel/Barrel'
import { HitMissEffect } from './ui/HitMissEffect'
import {
    EnumJSBridgeWebView,
    GameCloseType,
    JSBridgeClient,
    JSBridgeWebView,
} from '@/core/business/jsbridge/JSBridge'
import EnterGame from '@/core/business/hooks/EnterGame'
import { GameVisiable } from '@/core/business/store/global'
import { sleep } from '@/core/business/util/TimeUtils'

const { ccclass, property } = _decorator

@ccclass('Game')
export class Game extends SceneLayer {
    @property({ type: Node, tooltip: '桌面信息节点' })
    dashboard_node: Node = null!
    @property({ type: Node, tooltip: '卡牌信息节点' })
    card_info_node: Node = null!

    @property({ type: Desk, tooltip: '牌桌' })
    desk: Desk = null!

    // @property({ type: Prefab, tooltip: '出局UI预制体' })
    // player_out_ui_prefab: Prefab = null!

    // @property({ type: sp.Skeleton, tooltip: '出局骨骼动画' })
    // out_spine: sp.Skeleton = null!

    @property({ type: Node, tooltip: '观战节点' })
    watch_node: Node = null!

    @property({ type: Barrel, tooltip: '木桶' })
    barrel: Barrel = null!

    @property({ type: Node, tooltip: '玩家节点' })
    players_node: Node

    @property({ type: HitMissEffect, tooltip: '命中/未命中特效组件' })
    hitMissEffect: HitMissEffect = null!

    private needCleanSceneOnGameStart: boolean = false
    private needRestartGameOnEventShow: boolean = false
    private gameStartParams: string = ''

    override async start() {
        store.game.reset()

        cat.platform.inputMode(1)
        if (store.game.isParamsFromUrl) {
            JoinBattle(this.isReload)
        } else if (!store.game.gameLoadFinished) {
            // 第一次加载
            window.ccLog('正在登录中')
            cat.gui.showLoading({ title: '正在登录中' })
            store.game.gameLoadFinished = true
            JSBridgeClient.gameLoadFinished()
        } else {
            // 区分切后台重连和重新开局
            this.isReload && JoinBattle(this.isReload)
        }
    }

    protected override onLoad(): void {}

    protected override onAutoObserver(): void {
        this.addAutorun(() => {
            this.watch_node.active = store.user.isAudience
        })
    }

    override onDestroy(): void {
        cat.audio.stopMusic()
        cat.platform.inputMode(0)
        store.game.reset()
    }

    protected override onEventListener(): void {
        cat.event
            .on<SocketEvent>(
                'BattleInitialize',
                this.onBattleInitializeHandler,
                this
            )
            .on<SocketEvent>(
                'MT_PLAYER_INFO_MESSAGE',
                this.onGameUserInfoHandler,
                this
            )

            .on<SocketEvent>(
                'PlayerStatusChanged',
                this.onPlayerStatusChangedHandler,
                this
            )
            .on<SocketEvent>(
                'MT_GAME_OVER_BROADCAST',
                this.onEventFinishedHandler,
                this
            )
            .on(
                GameEventConstant.SHOW_CARD_INFO,
                this.onShowCardInfoHandler,
                this
            )
            .on(
                GameEventConstant.HIDE_CARC_INFO,
                this.onHideCardInfoHandler,
                this
            )
            .on(GlobalEventConstant.EVENT_SHOW, this.onShowHandler, this)
    }

    protected override addListener(): void {
        // 监听游戏开始
        JSBridgeWebView.on(
            EnumJSBridgeWebView.GAME_START,
            this.onGameStart,
            this
        )
        JSBridgeWebView.on(
            EnumJSBridgeWebView.MIKE_USERS_INFO,
            this.updataMikeUsersInfo,
            this
        )
    }

    protected override removeListener(): void {
        JSBridgeWebView.off(
            EnumJSBridgeWebView.GAME_START,
            this.onGameStart,
            this
        )

        JSBridgeWebView.off(
            EnumJSBridgeWebView.MIKE_USERS_INFO,
            this.updataMikeUsersInfo,
            this
        )
    }

    private onShowHandler() {
        if (this.needRestartGameOnEventShow) {
            this.onGameStart(this.gameStartParams)
            this.needRestartGameOnEventShow = false
            this.gameStartParams = ''
        }
    }

    private async onGameStart(data: string) {
        // 开始游戏时处于后台状态
        if (!store.global.isGameVisiable) {
            this.gameStartParams = data
            this.needRestartGameOnEventShow = true
            return
        }
        // 上一局结束时处于后台状态未清理场景
        if (this.needCleanSceneOnGameStart) {
            this.needCleanSceneOnGameStart = false
            await cat.gui.cleanScene()
        }
        window.ccLog('游戏开始 启动参数', data)
        try {
            cat.gui.showLoading({ title: '连接服务器' })
            // 开始游戏
            await EnterGame(data)
            store.game.gameLoadFinished = true
            JoinBattle()
        } catch (err) {
            error('监听游戏开始err', err)
            let message = '未知错误...'
            if (err instanceof Error) {
                message = err.message
            }
            JSBridgeClient.closeGame(
                GameCloseType.JoinOverTime,
                JSON.stringify({
                    err,
                    message,
                    info: '游戏开始错误',
                })
            )
        }
    }

    /**战局初始化 */
    private async onBattleInitializeHandler(_data: BattleInitialData) {
        cat.audio.playMusic(AudioEffectConstant.BGM)
        const { data, players, started, battle } = _data
        const syncResponse = fromBinary(SyncResponseSchema, data)
        const { gameInfo, playerInfo } = syncResponse
        window.ccLog('战局同步', syncResponse)
        window.ccLog('基础玩家', players)
        window.ccLog('战局初始化', started)
        window.ccLog('战局信息', battle)
        // 初始化
        const { game } = store
        const teams: Map<string, BasePlayer[]> = new Map()
        store.game.basePlayers = players

        // store.game.over_time_draw = draw

        store.game.roomData = gameInfo!
        store.game.playerInfo = playerInfo!

        battle && (store.game.battle = battle)

        players.forEach((item) => {
            if (!teams.has(item.teamId)) {
                teams.set(item.teamId, [])
            }
            const playes = teams.get(item.teamId)
            playes?.push(item)
        })

        game.teams = teams

        window.ccLog(teams)

        this.desk.initPlayers()

        if (started) {
            //已经开始
            // 同步界面
            await this.syncHandle(syncResponse)
            // 初始化鲨鱼牙齿
            this.barrel.initTooth(store.game.roomData.deckInfo?.deckCardStates)
        } else {
            cat.tracking.game.roomWait()
            cat.gui.showLoading({ title: '等待其他玩家加入...' })
        }

        JSBridgeClient.getMikeUsersInfo().then((res) => {
            console.log('平台用户装扮信息========', JSON.stringify(res))
            this.updataMikeUsersInfo(res)
        })
    }

    /**用户信息-广播 */
    private onGameUserInfoHandler(e: PlayerInfoMessage) {
        cat.gui.hideLoading()
    }

    private onPlayerStatusChangedHandler(data: BasePlayer) {
        const basePlayer = store.game.getBasePlayerByUid(data.id)
        if (basePlayer) {
            basePlayer.online = data.online
        }

        const player = store.game.getPlayerByUid(data.id)
        if (player) {
            player.hosting = data.hosting
        }
    }

    /**显示卡牌信息 */
    private onShowCardInfoHandler({ card }: { card: Card }) {
        // 显示卡牌信息提示
        this.card_info_node.getComponent(CardInfo)!.showCardInfo(card)
        this.dashboard_node.active = false
    }

    private onHideCardInfoHandler() {
        this.dashboard_node.active = true
    }

    async syncHandle(res: SyncResponse): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            const { gameInfo, playerInfo } = res

            const { deckDiscardCards } = gameInfo!.deckInfo!
            const { currentPlayerCursor } = gameInfo!.roundInfo!
            const player = store.game.currentPlayer
            const state = gameInfo?.stateInfo?.state

            if (state != DeskState.UNSPECIFIED) {
                //游戏处于未开始阶段
                if (state === DeskState.GAME_FINISHED) {
                    cat.gui.hideLoading().showLoading({ title: '游戏已结束' }) //显示loading调用结算
                } else {
                    // 同步出牌区最后一张牌
                    this.desk.init()
                    deckDiscardCards.length >= 1 &&
                        this.desk.addPlayCard(
                            deckDiscardCards[deckDiscardCards.length - 1]
                        )
                    if (!store.user.isAudience) {
                        this.desk.initHandCard(player!.handCards, true)
                        window.ccLog('同步手牌结束')
                    }
                    // 同步桌子状态
                    cat.event.dispatchEvent<SocketEvent>(
                        'MT_GAME_INFO_BROADCAST',
                        gameInfo
                    )
                    // 同步桌子状态
                    cat.event.dispatchEvent<SocketEvent>(
                        'MT_PLAYER_INFO_MESSAGE',
                        playerInfo
                    )
                    if (state === DeskState.PLAYER_STOLE) {
                        //响应索取阶段
                        cat.event.dispatchEvent<SocketEvent>(
                            'MT_PLAYER_AFTER_SELECT_TARGET_BROADCAST',
                            create(PlayerAfterSelectTargetBroadcastSchema, {
                                targetPlayerIndex: currentPlayerCursor,
                                // requestIndex: preCursor,
                            })
                        )
                    }
                    cat.gui.hideLoading()
                }

                resolve()
            } else {
                //游戏处于带开局阶段
                reject()
            }
        })
    }

    private async onEventFinishedHandler() {
        await sleep(3000)
        window.ccLog('对局结束： onEventFinishedHandler')
        cat.event
            .dispatchEvent(GameEventConstant.CLOSE_COMMON_UI)
            .dispatchEvent(GameEventConstant.UPDATE_PLAYER_STATE)
        // cat.audio.playEffect(AudioEffectConstant.GAME_OVER)

        cat.gui.showLoading({ title: '结算中...' }) //显示loading调用结算

        if (store.global.isChannelWeb) {
            window.history.back()
            return
        }
        cat.ws.destroy()

        if (store.global.isGameVisiable) {
            store.reset()
            store.game.gameLoadFinished = true
            await cat.gui.cleanScene()
        } else {
            store.reset()
            store.game.gameLoadFinished = true
            store.global.gameVisiable = GameVisiable.HIDE
            this.needCleanSceneOnGameStart = true
        }

        JSBridgeClient.closeGame()
    }

    updataMikeUsersInfo(data: any) {
        let tempdata = data
        if (typeof data == 'string') {
            tempdata = JSON.parse(data)
        }
        console.log('平台头像信息==========', JSON.stringify(tempdata))
        store.game.paltformPlayInfo = tempdata
        cat.event.dispatchEvent(RoomEventConstant.UPDATE_PLATFORM)
    }
}
