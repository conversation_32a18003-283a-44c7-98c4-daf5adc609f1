import { _decorator, Node, sp, isValid } from 'cc'
import { cat } from '@/core/manager'
import { SocketEvent } from '@/core/business/ws'
import { AudioEffectConstant } from '@/core/business/constant'
import {
    Card,
    CardActiveDefenseBroadcast,
    CardActiveSubstituteBroadcast,
    CardStrengthenInvalidBroadcast,
} from '@/pb-generate/server/pirate/v1/card_pb'
import { State as DeskState } from '@/pb-generate/server/pirate/v1/state_pb'

import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { CommonAction } from './common/CommonAction'
import { SelectPlayerAction } from './common/SelectPlayerAction'
import store from '@/core/business/store'

const { ccclass, property } = _decorator

/**
 * <替身 卡牌处理逻辑>
 */
@ccclass('SubstituteCardAction')
export class SubstituteCardAction extends SelectPlayerAction {
    protected override onEventListener(): void {
        cat.event.on<SocketEvent>(
            'MT_CARD_ACTIVE_SUBSTITUTE_BROADCAST',
            this.onActiveSubstitute,
            this
        )
    }

    protected override onAutoObserver(): void {
        super.onAutoObserver()
        this.addReaction(
            () => [
                store.game.roomData.stateInfo?.state,
                store.game.lastRoomData.stateInfo?.state,
            ],
            ([state, lastState]) => {
                if (
                    state === DeskState.PLAYER_SELECT_TARGET &&
                    lastState === DeskState.CARD_ACTIVE_SUBSTITUTE
                ) {
                    window.ccLog(
                        '替身激活阶段',
                        state === DeskState.PLAYER_SELECT_TARGET
                    )
                    // cat.audio.playEffect(AudioEffectConstant.EXCHANGE)
                    this.onStateGameSelectTarget(store.game.roomData, true)
                }
            }
        )
    }

    private onActiveSubstitute(data: CardActiveSubstituteBroadcast) {
        cat.audio.playEffect(AudioEffectConstant.TRANSFER)
        this.getComponent(CommonAction)?.showCardEffect(Card.SUBSTITUTE)
        if (
            !data.hasSelectablePlayer &&
            data.activatedPlayerIndex === store.user.userIndex
        ) {
            cat.gui.showToast({
                title: '没有可以指定的替身，替身的效果转化为防御',
            })
        }
    }
}
