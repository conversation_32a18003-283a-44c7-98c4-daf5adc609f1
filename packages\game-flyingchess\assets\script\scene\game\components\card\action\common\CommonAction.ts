import {
    _decorator,
    Component,
    error,
    instantiate,
    isValid,
    log,
    Node,
    Prefab,
    sp,
    UITransform,
    Vec3,
} from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { Card } from '@/pb-generate/server/pirate/v1/card_pb'
import { BaseAction } from './BaseAction'

const { ccclass, property } = _decorator

export type AnimationCard =
    | Card.FREEZE
    | Card.DEFENSE
    | Card.PEEK
    | Card.STRENGTHEN
    | Card.WEAKEN
    | Card.SUBSTITUTE
    | Card.REVIVE
    | Card.SWAP
    | Card.DEATH

@ccclass('CommonAction')
export class CommonAction<T extends Object> extends BaseAction<T> {
    @property({ type: Node, tooltip: '遮罩层节点' })
    mask_node: Node

    @property({ type: sp.Skeleton, tooltip: 'freeze动画节点' })
    freeze_node: sp.Skeleton

    @property({ type: sp.Skeleton, tooltip: 'defense动画节点' })
    defense_node: sp.Skeleton

    @property({ type: sp.Skeleton, tooltip: 'peek动画节点' })
    peek_node: sp.Skeleton

    @property({ type: sp.Skeleton, tooltip: 'strengthen动画节点' })
    strengthen_node: sp.Skeleton

    @property({ type: sp.Skeleton, tooltip: 'weeken动画节点' })
    weeken_node: sp.Skeleton

    @property({ type: sp.Skeleton, tooltip: 'substitute动画节点' })
    substitute_node: sp.Skeleton

    @property({ type: sp.Skeleton, tooltip: 'revive动画节点' })
    revive_node: sp.Skeleton

    @property({ type: sp.Skeleton, tooltip: 'swap动画节点' })
    swap_node: sp.Skeleton

    @property({ type: sp.Skeleton, tooltip: 'death动画节点' })
    death_node: sp.Skeleton

    private animationMap: Map<
        AnimationCard,
        {
            name: string
            skeleton: sp.Skeleton
        }
    >

    private currentAnimation:
        | {
              name: string
              skeleton: sp.Skeleton
          }
        | null
        | undefined = null

    protected override onLoad(): void {
        this.animationMap = new Map([
            [
                Card.FREEZE,
                {
                    name: 'admission',
                    skeleton: this.freeze_node,
                },
            ],
            [
                Card.DEFENSE,
                {
                    name: 'idle',
                    skeleton: this.defense_node,
                },
            ],
            [
                Card.PEEK,
                {
                    name: 'idle',
                    skeleton: this.peek_node,
                },
            ],
            [
                Card.STRENGTHEN,
                {
                    name: 'idle',
                    skeleton: this.strengthen_node,
                },
            ],
            [
                Card.WEAKEN,
                {
                    name: 'feeble1',
                    skeleton: this.weeken_node,
                },
            ],
            [
                Card.SUBSTITUTE,
                {
                    name: 'scapegoat_admission',
                    skeleton: this.substitute_node,
                },
            ],
            [
                Card.REVIVE,
                {
                    name: 'rebirth1',
                    skeleton: this.revive_node,
                },
            ],
            [
                Card.SWAP,
                {
                    name: 'idle',
                    skeleton: this.swap_node,
                },
            ],
            [
                Card.DEATH,
                {
                    name: 'idle',
                    skeleton: this.death_node,
                },
            ],
        ])
    }

    /**
     * 显示卡牌特效
     * @param {CardEffect} type  卡牌类型
     */
    showCardEffect(type: AnimationCard) {
        // this.hideTween()
        // 隐藏其他特效
        this.animationMap.forEach((item, key) => {
            const skeleton_node = item.skeleton?.node
            skeleton_node && (skeleton_node.active = false)
        })

        this.currentAnimation = this.animationMap.get(type)!
        const skeleton = this.currentAnimation.skeleton
        const name = this.currentAnimation.name

        window.ccLog(
            `【type】:${type}  【播放动作】:${name}  【skeleton】:`,
            skeleton
        )
        this.doPlay(name)
    }

    hideTween() {
        if (this.currentAnimation) {
            this.currentAnimation.skeleton.node.active = false
        }
    }

    // 指定时间播放
    doPlay(name: string) {
        if (!name.length) return error(`指定播放name为空`)
        const spine = this.currentAnimation!.skeleton
        const cb = () => {
            window.ccLog('spice回调')
            // TODO
            // spine.setCompleteListener(null)
            if (isValid(spine)) {
                window.ccLog('动画结束')
                spine.node.active = false
                this.mask_node.active = false
            }
        }
        spine.setCompleteListener(cb)
        this.mask_node.active = true
        this.currentAnimation!.skeleton.node.active = true

        spine.setAnimation(0, name, false)
        // spine.timeScale = 0
        // spine.timeScale = 1
        window.ccLog(
            '动画开始',
            this.currentAnimation!.skeleton,
            this.currentAnimation!.skeleton.node.active
        )
    }
}
