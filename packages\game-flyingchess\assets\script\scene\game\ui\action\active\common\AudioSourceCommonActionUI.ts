import { Label, _decorator, isValid, log } from 'cc'
import { cat } from '@/core/manager'
import { GameEventConstant } from '@/core/business/constant'
import AudioSourceUILayer from '@/core/manager/gui/layer/AudioSourceUILayer'

const { ccclass, property } = _decorator

@ccclass('AudioSourceCommonActionUI')
export class AudioSourceCommonActionUI<
    T extends object,
    U extends object = {}
> extends AudioSourceUILayer<T, U> {
    // @property({ type: Label, tooltip: '倒计时节点' })
    // time_count: Label = null!

    protected override start(): void {
        cat.event.dispatchEvent(GameEventConstant.CLOSE_TEAM_DROP_MINE)

        // const end_time: number = Date.now() + 5_000

        // const cb = () => {
        //     const diff = Math.round((end_time - Date.now()) / 1000)
        //     if (diff >= 0) {
        //         this.time_count.string = `(${diff}s)`
        //     } else {
        //         this.unschedule(cb)
        //         // 回到房间
        //         this.onUpdateStateHandler()
        //     }
        // }

        // this.schedule(cb, 1)
    }

    protected onUpdateStateHandler() {
        window.ccLog('UPDATE_STATE')

        this.onCloseCommonUIHandler()
    }

    protected onCloseCommonUIHandler() {
        if (isValid(this)) {
            window.ccLog('onCloseCommonUIHandler-->>', this.name)
            // 关闭界面
            cat.gui.closeUI(this, { isMotion: false })
        }
    }
}
