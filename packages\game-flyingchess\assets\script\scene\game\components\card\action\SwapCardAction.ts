import {
    _decorator,
    Component,
    instantiate,
    log,
    Node,
    Prefab,
    UITransform,
    v3,
} from 'cc'
import { SelectPlayerAction } from './common/SelectPlayerAction'

import { LightCardItem } from '../LightCardItem'
import { PlayerItem } from '../../player/PlayerItem'
import { HandCard } from '../../desk/HandCard'
import { cat } from '@/core/manager'
import { SocketEvent } from '@/core/business/ws'

// import {
//     Card,
//     DeskState,
//     ResponseExchangeBroadcast,
//     SelectTargetBroadcast,
// } from '@/pb-generate/server/shark/suileyoo/v1/msg_pb'
import {
    Card,
    CardAfterSwapMessage,
} from '@/pb-generate/server/pirate/v1/card_pb'
import { State as DeskState } from '@/pb-generate/server/pirate/v1/state_pb'
import {
    AudioEffectConstant,
    GameEventConstant,
} from '@/core/business/constant'
import store from '@/core/business/store'
import { audioEffect } from '@/core/business/hooks/Decorator'
import { CommonAction } from './common/CommonAction'

const { ccclass, property } = _decorator

/**
 * <交换卡牌处理逻辑>
 */
@ccclass('SwapCardAction')
export class SwapCardAction extends SelectPlayerAction {
    protected override onEventListener(): void {
        super.onEventListener()
        cat.event.on<SocketEvent>(
            'MT_CARD_AFTER_SWAP_MESSAGE',
            this.onCardAfterSwapMessageHandler,
            this
        )
    }

    protected override onAutoObserver(): void {
        super.onAutoObserver
        this.addReaction(
            () => store.game.roomData.stateInfo?.state,
            (state) => {
                if (state === DeskState.PLAYER_SELECT_TARGET) {
                    if (Card.SWAP === store.game.getLastCardOnPostZone) {
                        window.ccLog(
                            '调包阶段',
                            state === DeskState.PLAYER_SELECT_TARGET
                        )
                        // cat.audio.playEffect(AudioEffectConstant.EXCHANGE)
                        this.onStateGameSelectTarget(store.game.roomData)
                    }
                }
            }
        )
    }

    onCardAfterSwapMessageHandler(data: CardAfterSwapMessage) {
        this.scheduleOnce(() => {
            this.onCardAfterSwapMessage(data)
        }, 0.2)
    }

    // A向B交换 B(2)被交换者A(0)
    // @audioEffect(AudioEffectConstant.EXCHANGE_FINISH)
    private onCardAfterSwapMessage(data: CardAfterSwapMessage) {
        window.ccLog(
            `${data.fromPlayerIndex}向${data.targetPlayerIndex}发起指向`
        )
        cat.audio.playEffect(AudioEffectConstant.EXCHANGE)

        // 响应节点
        const from = store.game.getPlayerComponentByIndex(
            this.desk.players_node,
            data.targetPlayerIndex
        )!.node
        // 请求节点
        const to = store.game.getPlayerComponentByIndex(
            this.desk.players_node,
            data.fromPlayerIndex
        )!.node

        /**用户是[交换者]或者[交换者]的观战队友 || 用户是[被交换者]或者[被交换者]的观战队友 */
        if (
            store.game.isUserOrWatchTeammate(data.targetPlayerIndex) ||
            store.game.isUserOrWatchTeammate(data.fromPlayerIndex)
        ) {
            this.cardAnimationManager.swapTween({
                targetPlayer: store.game.isUserOrWatchTeammate(
                    data.targetPlayerIndex
                )
                    ? to
                    : from,
                cards: data.playerHandCards,
            })
        } else {
            this.getComponent(CommonAction)?.showCardEffect(Card.SWAP)
        }
    }
}
