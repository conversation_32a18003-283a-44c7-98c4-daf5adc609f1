[{"__type__": "cc.SceneAsset", "_name": "game", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "game", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 10}, {"__id__": 32}, {"__id__": 35}, {"__id__": 49}], "_active": true, "_components": [], "_prefab": {"__id__": 55}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": false, "_globals": {"__id__": 181}, "_id": "00f825dd-1bc3-4e9b-a0f5-b35c2b69630f"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 3}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "27213ebb-4f9d-4f75-9c00-f08e7fa13c59", "__expectedType__": "cc.Prefab"}, "fileId": "51LmPBqq9Aa75XI9LNsDHn", "instance": {"__id__": 4}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "25jSSyoq5IHanxd2n4zB/c", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 5}, {"__id__": 7}, {"__id__": 8}, {"__id__": 9}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_name"], "value": "Canvas BG"}, {"__type__": "cc.TargetInfo", "localID": ["51LmPBqq9Aa75XI9LNsDHn"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 540, "y": 960, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 512, "_parent": {"__id__": 1}, "_prefab": {"__id__": 11}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 10}, "asset": {"__uuid__": "85cbc930-ae1f-4a64-a7ff-76374ad0b9da", "__expectedType__": "cc.Prefab"}, "fileId": "09xhj0O2ZHDKDD37W3spS6", "instance": {"__id__": 12}, "targetOverrides": []}, {"__type__": "cc.PrefabInstance", "fileId": "0fEB4bgoVK06CIkF4fVAke", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 13}, {"__id__": 15}, {"__id__": 16}, {"__id__": 18}, {"__id__": 19}, {"__id__": 21}, {"__id__": 22}, {"__id__": 23}, {"__id__": 24}, {"__id__": 26}, {"__id__": 27}, {"__id__": 28}, {"__id__": 29}, {"__id__": 30}, {"__id__": 31}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 14}, "propertyPath": ["_shutter"], "value": 7}, {"__type__": "cc.TargetInfo", "localID": ["52alvLXU5H3J2ZQxfApOqQ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 14}, "propertyPath": ["_aperture"], "value": 18}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 17}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["60brthFahOe5fLeB84ZoiR"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 17}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 20}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -0.8, "y": 0, "z": -12}}, {"__type__": "cc.TargetInfo", "localID": ["672LXREbVC56l9tmRG9b8Z"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 14}, "propertyPath": ["_far"], "value": 1000}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 20}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0.29237170472273677, "y": 0, "z": 0, "w": 0.9563047559630354}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 20}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 34, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 25}, "propertyPath": ["bakeSettings", "_bakeToLightProbe"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["d1ptu5bqVJMa0aeFshPKOJ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 25}, "propertyPath": ["bakeSettings", "_useLightProbe"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 25}, "propertyPath": ["bakeSettings", "_reflectionProbeType"], "value": 2}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 25}, "propertyPath": ["bakeSettings", "_bakeToReflectionProbe"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 25}, "propertyPath": ["_shadowCastingMode"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 25}, "propertyPath": ["_shadowReceivingMode"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 20}, "propertyPath": ["_name"], "value": "barrel3D"}, {"__type__": "cc.Node", "_name": "Directional Light-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 33}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -11}, "_lrot": {"__type__": "cc.Quat", "x": -0.4269836809206792, "y": 0.49316040861863236, "z": 0.4573381886388414, "w": 0.6044166847557029}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 0.9999999999999999, "z": 1}, "_mobility": 0, "_layer": 8388608, "_euler": {"__type__": "cc.Vec3", "x": -77.352, "y": 84.485, "z": 7.568}, "_id": "79NNNjUDdD5rcRXBMPDRTa"}, {"__type__": "cc.DirectionalLight", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 32}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useColorTemperature": true, "_colorTemperature": 7500, "_staticSettings": {"__id__": 34}, "_visibility": 1083949056, "_illuminanceHDR": 140000, "_illuminance": 140000, "_illuminanceLDR": 1.6927083333333335, "_shadowEnabled": false, "_shadowPcf": 3, "_shadowBias": 1e-05, "_shadowNormalBias": 0, "_shadowSaturation": 1, "_shadowDistance": 118.2, "_shadowInvisibleOcclusionRange": 200, "_csmLevel": 4, "_csmLayerLambda": 0.75, "_csmOptimizationMode": 2, "_csmAdvancedOptions": false, "_csmLayersTransition": false, "_csmTransitionRange": 0.05, "_shadowFixedArea": false, "_shadowNear": 0.1, "_shadowFar": 10, "_shadowOrthoSize": 5, "_id": "3enkxOjKNHfIaVXwvgqHmr"}, {"__type__": "cc.StaticLightSettings", "_baked": false, "_editorOnly": false, "_castShadow": true}, {"__type__": "cc.Node", "_name": "Game UI", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 36}, {"__id__": 38}], "_active": true, "_components": [{"__id__": 46}, {"__id__": 47}, {"__id__": 48}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 540, "y": 960, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4a0popXrhIpqqoQRIJvNaZ"}, {"__type__": "cc.Node", "_name": "Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 35}, "_children": [], "_active": true, "_components": [{"__id__": 37}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "609qrF5ylB36axxkKULhOA"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 36}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 4, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 960, "_near": 0, "_far": 1000, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 6, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 16384, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "ffxI3nRvFH7qnX3KwEIfSg"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 35}, "_prefab": {"__id__": 39}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 38}, "asset": {"__uuid__": "d5b584bf-6d4d-437a-a4c6-910d53302c68", "__expectedType__": "cc.Prefab"}, "fileId": "80wh8UaLNO3oPFwlRH61l0", "instance": {"__id__": 40}, "targetOverrides": []}, {"__type__": "cc.PrefabInstance", "fileId": "21ROjKvGNLjpFN7G+6qbX2", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 41}, {"__id__": 43}, {"__id__": 44}, {"__id__": 45}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 42}, "propertyPath": ["_name"], "value": "GameUIMain"}, {"__type__": "cc.TargetInfo", "localID": ["80wh8UaLNO3oPFwlRH61l0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 42}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 42}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 42}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 35}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "76+PQdmbRMn5SlBao0J/oA"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 35}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 37}, "_alignCanvasWithScreen": true, "_id": "2a++VycSRAoo5OSojwy9lu"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 35}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "bdr8LNBXpJQLOyWEuJWns5"}, {"__type__": "cc.Node", "_name": "Draw", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 50}], "_active": true, "_components": [{"__id__": 52}, {"__id__": 53}, {"__id__": 54}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 540, "y": 960, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 131072, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "86BqXqTHRLr5qJpcyRuZdP"}, {"__type__": "cc.Node", "_name": "CameraDraw", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 49}, "_children": [], "_active": true, "_components": [{"__id__": 51}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2dKgZcUjBE0oEMcFlmJALD"}, {"__type__": "cc.Camera", "_name": "Camera<CameraComponent>", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 50}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 101, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 960, "_near": 1, "_far": 1000, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 6, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 131072, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "b091FXFetF0Y0j0EJniQUI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 49}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "efyI4igCtF6LfXMtzcrm0h"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 49}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 51}, "_alignCanvasWithScreen": true, "_id": "faP4+ak0tI6Zvl36f6ByHp"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 49}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1080, "_originalHeight": 1920, "_alignMode": 2, "_lockFlags": 0, "_id": "40xQNIzSFGpL6mYPq4qKQY"}, {"__type__": "cc.PrefabInfo", "root": null, "asset": null, "fileId": "00f825dd-1bc3-4e9b-a0f5-b35c2b69630f", "instance": null, "targetOverrides": [{"__id__": 56}, {"__id__": 59}, {"__id__": 62}, {"__id__": 65}, {"__id__": 67}, {"__id__": 70}, {"__id__": 73}, {"__id__": 76}, {"__id__": 79}, {"__id__": 82}, {"__id__": 85}, {"__id__": 88}, {"__id__": 91}, {"__id__": 94}, {"__id__": 97}, {"__id__": 100}, {"__id__": 103}, {"__id__": 106}, {"__id__": 109}, {"__id__": 112}, {"__id__": 115}, {"__id__": 118}, {"__id__": 121}, {"__id__": 124}, {"__id__": 127}, {"__id__": 130}, {"__id__": 133}, {"__id__": 136}, {"__id__": 139}, {"__id__": 142}, {"__id__": 145}, {"__id__": 148}, {"__id__": 151}, {"__id__": 154}, {"__id__": 157}, {"__id__": 160}, {"__id__": 163}, {"__id__": 166}, {"__id__": 169}, {"__id__": 172}, {"__id__": 175}, {"__id__": 178}], "nestedPrefabInstanceRoots": [{"__id__": 2}, {"__id__": 10}, {"__id__": 38}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 10}, "sourceInfo": {"__id__": 57}, "propertyPath": ["base2D"], "target": {"__id__": 2}, "targetInfo": {"__id__": 58}}, {"__type__": "cc.TargetInfo", "localID": ["4f5yfQH85BnqDArNzkZU61"]}, {"__type__": "cc.TargetInfo", "localID": ["41l5n+e+tOYZTSFPPKnL41"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 10}, "sourceInfo": {"__id__": 60}, "propertyPath": ["camera2D"], "target": {"__id__": 2}, "targetInfo": {"__id__": 61}}, {"__type__": "cc.TargetInfo", "localID": ["4f5yfQH85BnqDArNzkZU61"]}, {"__type__": "cc.TargetInfo", "localID": ["94SgnZKnxAuIpPViB4/L9Q"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 63}, "sourceInfo": null, "propertyPath": ["camera2D"], "target": {"__id__": 2}, "targetInfo": {"__id__": 64}}, {"__type__": "cc.Node", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9d1CoFss1OoY/HIF0CP8wr"}, {"__type__": "cc.TargetInfo", "localID": ["94SgnZKnxAuIpPViB4/L9Q"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 63}, "sourceInfo": null, "propertyPath": ["base2D"], "target": {"__id__": 2}, "targetInfo": {"__id__": 66}}, {"__type__": "cc.TargetInfo", "localID": ["41l5n+e+tOYZTSFPPKnL41"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 10}, "sourceInfo": {"__id__": 68}, "propertyPath": ["camera2D"], "target": {"__id__": 2}, "targetInfo": {"__id__": 69}}, {"__type__": "cc.TargetInfo", "localID": ["1cEvBxcWNK1pLIRam8jaXZ"]}, {"__type__": "cc.TargetInfo", "localID": ["94SgnZKnxAuIpPViB4/L9Q"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 10}, "sourceInfo": {"__id__": 71}, "propertyPath": ["base2D"], "target": {"__id__": 2}, "targetInfo": {"__id__": 72}}, {"__type__": "cc.TargetInfo", "localID": ["1cEvBxcWNK1pLIRam8jaXZ"]}, {"__type__": "cc.TargetInfo", "localID": ["41l5n+e+tOYZTSFPPKnL41"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 10}, "sourceInfo": {"__id__": 74}, "propertyPath": ["camera2D"], "target": {"__id__": 2}, "targetInfo": {"__id__": 75}}, {"__type__": "cc.TargetInfo", "localID": ["3dketsr4ZGuZqTMveNHNr8"]}, {"__type__": "cc.TargetInfo", "localID": ["94SgnZKnxAuIpPViB4/L9Q"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 10}, "sourceInfo": {"__id__": 77}, "propertyPath": ["base2D"], "target": {"__id__": 2}, "targetInfo": {"__id__": 78}}, {"__type__": "cc.TargetInfo", "localID": ["3dketsr4ZGuZqTMveNHNr8"]}, {"__type__": "cc.TargetInfo", "localID": ["39Se0yKi1OWY4tdCC1T3F1"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 80}, "propertyPath": ["barrel"], "target": {"__id__": 10}, "targetInfo": {"__id__": 81}}, {"__type__": "cc.TargetInfo", "localID": ["b1ZPhqWmxN95CI7wwPjPc0"]}, {"__type__": "cc.TargetInfo", "localID": ["3dketsr4ZGuZqTMveNHNr8"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 83}, "propertyPath": ["barrel"], "target": {"__id__": 10}, "targetInfo": {"__id__": 84}}, {"__type__": "cc.TargetInfo", "localID": ["ab0acimsZAObN0fFUXyYvS"]}, {"__type__": "cc.TargetInfo", "localID": ["3dketsr4ZGuZqTMveNHNr8"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 86}, "propertyPath": ["camera3D"], "target": {"__id__": 10}, "targetInfo": {"__id__": 87}}, {"__type__": "cc.TargetInfo", "localID": ["ab0acimsZAObN0fFUXyYvS"]}, {"__type__": "cc.TargetInfo", "localID": ["52alvLXU5H3J2ZQxfApOqQ"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 10}, "sourceInfo": {"__id__": 89}, "propertyPath": ["camera3D"], "target": {"__id__": 10}, "targetInfo": {"__id__": 90}}, {"__type__": "cc.TargetInfo", "localID": ["3dketsr4ZGuZqTMveNHNr8"]}, {"__type__": "cc.TargetInfo", "localID": ["52alvLXU5H3J2ZQxfApOqQ"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 10}, "sourceInfo": {"__id__": 92}, "propertyPath": ["barrel3D"], "target": {"__id__": 10}, "targetInfo": {"__id__": 93}}, {"__type__": "cc.TargetInfo", "localID": ["3dketsr4ZGuZqTMveNHNr8"]}, {"__type__": "cc.TargetInfo", "localID": ["672LXREbVC56l9tmRG9b8Z"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 10}, "sourceInfo": {"__id__": 95}, "propertyPath": ["barrel_rotate"], "target": {"__id__": 10}, "targetInfo": {"__id__": 96}}, {"__type__": "cc.TargetInfo", "localID": ["3dketsr4ZGuZqTMveNHNr8"]}, {"__type__": "cc.TargetInfo", "localID": ["0bWaUfREZLbYhzSUdySfYY"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 10}, "sourceInfo": {"__id__": 98}, "propertyPath": ["base3D"], "target": {"__id__": 10}, "targetInfo": {"__id__": 99}}, {"__type__": "cc.TargetInfo", "localID": ["4f5yfQH85BnqDArNzkZU61"]}, {"__type__": "cc.TargetInfo", "localID": ["672LXREbVC56l9tmRG9b8Z"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 10}, "sourceInfo": {"__id__": 101}, "propertyPath": ["camera3D"], "target": {"__id__": 10}, "targetInfo": {"__id__": 102}}, {"__type__": "cc.TargetInfo", "localID": ["4f5yfQH85BnqDArNzkZU61"]}, {"__type__": "cc.TargetInfo", "localID": ["52alvLXU5H3J2ZQxfApOqQ"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 10}, "sourceInfo": {"__id__": 104}, "propertyPath": ["only_barrel"], "target": {"__id__": 10}, "targetInfo": {"__id__": 105}}, {"__type__": "cc.TargetInfo", "localID": ["3dketsr4ZGuZqTMveNHNr8"]}, {"__type__": "cc.TargetInfo", "localID": ["f39tBqHTBBH5wRg2HYe5AZ"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 10}, "sourceInfo": {"__id__": 107}, "propertyPath": ["top_hole_0"], "target": {"__id__": 10}, "targetInfo": {"__id__": 108}}, {"__type__": "cc.TargetInfo", "localID": ["3dketsr4ZGuZqTMveNHNr8"]}, {"__type__": "cc.TargetInfo", "localID": ["8536vbBbFIJK6mPWEfSxV3", "c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 10}, "sourceInfo": {"__id__": 110}, "propertyPath": ["top_hole_1"], "target": {"__id__": 10}, "targetInfo": {"__id__": 111}}, {"__type__": "cc.TargetInfo", "localID": ["3dketsr4ZGuZqTMveNHNr8"]}, {"__type__": "cc.TargetInfo", "localID": ["cfLx6PYyhG9byVW0+AMThG", "c46/YsCPVOJYA4mWEpNYRx"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 113}, "propertyPath": ["camera3D"], "target": {"__id__": 10}, "targetInfo": {"__id__": 114}}, {"__type__": "cc.TargetInfo", "localID": ["44xVtBfcNAtpIWdxoBGH9S"]}, {"__type__": "cc.TargetInfo", "localID": ["52alvLXU5H3J2ZQxfApOqQ"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 116}, "propertyPath": ["hitMissEffect"], "target": {"__id__": 2}, "targetInfo": {"__id__": 117}}, {"__type__": "cc.TargetInfo", "localID": ["b1ZPhqWmxN95CI7wwPjPc0"]}, {"__type__": "cc.TargetInfo", "localID": ["59z8KWP15Ap4nxyd3Ip8GZ"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 119}, "propertyPath": ["effect_container"], "target": {"__id__": 38}, "targetInfo": {"__id__": 120}}, {"__type__": "cc.TargetInfo", "localID": ["44xVtBfcNAtpIWdxoBGH9S"]}, {"__type__": "cc.TargetInfo", "localID": ["0eu9dRRXxPl4Gis29JSzy7"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 122}, "propertyPath": ["hitMissSpine"], "target": {"__id__": 38}, "targetInfo": {"__id__": 123}}, {"__type__": "cc.TargetInfo", "localID": ["44xVtBfcNAtpIWdxoBGH9S"]}, {"__type__": "cc.TargetInfo", "localID": ["3cjfciJcJBOp8PnuVcOK5k"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 125}, "propertyPath": ["effect_container"], "target": {"__id__": 2}, "targetInfo": {"__id__": 126}}, {"__type__": "cc.TargetInfo", "localID": ["c2mUUHeYFJsKX9F4sejQu8"]}, {"__type__": "cc.TargetInfo", "localID": ["40zoZWy0pMtbTBd32NCnJb"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 128}, "propertyPath": ["hitMissSpine"], "target": {"__id__": 2}, "targetInfo": {"__id__": 129}}, {"__type__": "cc.TargetInfo", "localID": ["c2mUUHeYFJsKX9F4sejQu8"]}, {"__type__": "cc.TargetInfo", "localID": ["00sjMI+LlPHp3t+nY/UaIH"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 131}, "propertyPath": ["dashboard_node"], "target": {"__id__": 38}, "targetInfo": {"__id__": 132}}, {"__type__": "cc.TargetInfo", "localID": ["b1ZPhqWmxN95CI7wwPjPc0"]}, {"__type__": "cc.TargetInfo", "localID": ["04dCTasO1IwIkewBI9nPtF"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 134}, "propertyPath": ["card_info_node"], "target": {"__id__": 38}, "targetInfo": {"__id__": 135}}, {"__type__": "cc.TargetInfo", "localID": ["b1ZPhqWmxN95CI7wwPjPc0"]}, {"__type__": "cc.TargetInfo", "localID": ["cbgfCXUC5NHp4xsCOs6MOn"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 137}, "propertyPath": ["desk"], "target": {"__id__": 38}, "targetInfo": {"__id__": 138}}, {"__type__": "cc.TargetInfo", "localID": ["b1ZPhqWmxN95CI7wwPjPc0"]}, {"__type__": "cc.TargetInfo", "localID": ["ab0acimsZAObN0fFUXyYvS"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 140}, "propertyPath": ["watch_node"], "target": {"__id__": 38}, "targetInfo": {"__id__": 141}}, {"__type__": "cc.TargetInfo", "localID": ["b1ZPhqWmxN95CI7wwPjPc0"]}, {"__type__": "cc.TargetInfo", "localID": ["b5cLKFwfBE/Jy8/uw+mcsj"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 143}, "propertyPath": ["players_node"], "target": {"__id__": 38}, "targetInfo": {"__id__": 144}}, {"__type__": "cc.TargetInfo", "localID": ["b1ZPhqWmxN95CI7wwPjPc0"]}, {"__type__": "cc.TargetInfo", "localID": ["b017N9VFRNOaIfgPG2kZg0"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 146}, "propertyPath": ["card_move_tween"], "target": {"__id__": 38}, "targetInfo": {"__id__": 147}}, {"__type__": "cc.TargetInfo", "localID": ["ab0acimsZAObN0fFUXyYvS"]}, {"__type__": "cc.TargetInfo", "localID": ["89iJoLNLtPLahOUr0ph3yM"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 149}, "propertyPath": ["draw_zone"], "target": {"__id__": 38}, "targetInfo": {"__id__": 150}}, {"__type__": "cc.TargetInfo", "localID": ["ab0acimsZAObN0fFUXyYvS"]}, {"__type__": "cc.TargetInfo", "localID": ["2bOJG2Ky9HH4wsLfl5ZExk"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 152}, "propertyPath": ["draw_display_zone"], "target": {"__id__": 38}, "targetInfo": {"__id__": 153}}, {"__type__": "cc.TargetInfo", "localID": ["ab0acimsZAObN0fFUXyYvS"]}, {"__type__": "cc.TargetInfo", "localID": ["71kw+9Cc5NZL54ELNzzL6o"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 155}, "propertyPath": ["players_node"], "target": {"__id__": 38}, "targetInfo": {"__id__": 156}}, {"__type__": "cc.TargetInfo", "localID": ["ab0acimsZAObN0fFUXyYvS"]}, {"__type__": "cc.TargetInfo", "localID": ["31wXUAndFN/aRqxSa8R5Jb"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 158}, "propertyPath": ["remaining_deck_cards"], "target": {"__id__": 38}, "targetInfo": {"__id__": 159}}, {"__type__": "cc.TargetInfo", "localID": ["ab0acimsZAObN0fFUXyYvS"]}, {"__type__": "cc.TargetInfo", "localID": ["47hvb0VrhJp6GIdhwErGzb"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 161}, "propertyPath": ["boom_count"], "target": {"__id__": 38}, "targetInfo": {"__id__": 162}}, {"__type__": "cc.TargetInfo", "localID": ["ab0acimsZAObN0fFUXyYvS"]}, {"__type__": "cc.TargetInfo", "localID": ["34cuaDshlK9YBWCtuhUa5t"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 164}, "propertyPath": ["play_zone"], "target": {"__id__": 38}, "targetInfo": {"__id__": 165}}, {"__type__": "cc.TargetInfo", "localID": ["ab0acimsZAObN0fFUXyYvS"]}, {"__type__": "cc.TargetInfo", "localID": ["cacC5E64JGV6NcRslKaczj"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 167}, "propertyPath": ["hand_card"], "target": {"__id__": 38}, "targetInfo": {"__id__": 168}}, {"__type__": "cc.TargetInfo", "localID": ["ab0acimsZAObN0fFUXyYvS"]}, {"__type__": "cc.TargetInfo", "localID": ["25YgbtrkVMKLh2qpZAGyo9"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 170}, "propertyPath": ["action_node"], "target": {"__id__": 38}, "targetInfo": {"__id__": 171}}, {"__type__": "cc.TargetInfo", "localID": ["ab0acimsZAObN0fFUXyYvS"]}, {"__type__": "cc.TargetInfo", "localID": ["28ONigs71N6rqcEJUO9+ny"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 173}, "propertyPath": ["btn_action_draw"], "target": {"__id__": 38}, "targetInfo": {"__id__": 174}}, {"__type__": "cc.TargetInfo", "localID": ["ab0acimsZAObN0fFUXyYvS"]}, {"__type__": "cc.TargetInfo", "localID": ["b1KflMpspEoYvbWoMxt9TS"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 176}, "propertyPath": ["btn_action_play"], "target": {"__id__": 38}, "targetInfo": {"__id__": 177}}, {"__type__": "cc.TargetInfo", "localID": ["ab0acimsZAObN0fFUXyYvS"]}, {"__type__": "cc.TargetInfo", "localID": ["33QAOZ0KFGO61bnmSYDKa5"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": {"__id__": 179}, "propertyPath": ["your_trun_node"], "target": {"__id__": 38}, "targetInfo": {"__id__": 180}}, {"__type__": "cc.TargetInfo", "localID": ["ab0acimsZAObN0fFUXyYvS"]}, {"__type__": "cc.TargetInfo", "localID": ["a8pHtoZ6lKh6Nk8AlZhTOQ"]}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 182}, "shadows": {"__id__": 183}, "_skybox": {"__id__": 184}, "fog": {"__id__": 185}, "octree": {"__id__": 186}, "skin": {"__id__": 187}, "lightProbeInfo": {"__id__": 188}, "postSettings": {"__id__": 189}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0.9137254901960784, "y": 0.9607843137254902, "z": 1, "w": 1.04166625}, "_skyColor": {"__type__": "cc.Vec4", "x": 0.9137254901960784, "y": 0.9607843137254902, "z": 1, "w": 1.04166625}, "_skyIllumHDR": 40000, "_skyIllum": 40000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0.42745098039215684, "y": 0.42745098039215684, "z": 0.42745098039215684, "w": 1}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0.42745098039215684, "y": 0.42745098039215684, "z": 0.42745098039215684, "w": 1}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.452588, "y": 0.607642, "z": 0.755699, "w": 0}, "_skyIllumLDR": 0.8, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.618555, "y": 0.577848, "z": 0.544564, "w": 0}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 1, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_planeBias": 1, "_shadowColor": {"__type__": "cc.Color", "r": 76, "g": 76, "b": 76, "a": 255}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 2048, "y": 2048}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 1, "_envmapHDR": {"__uuid__": "d032ac98-05e1-4090-88bb-eb640dcb5fc1@b47c0", "__expectedType__": "cc.TextureCube"}, "_envmap": {"__uuid__": "d032ac98-05e1-4090-88bb-eb640dcb5fc1@b47c0", "__expectedType__": "cc.TextureCube"}, "_envmapLDR": {"__uuid__": "6f01cf7f-81bf-4a7e-bd5d-0afc19696480@b47c0", "__expectedType__": "cc.TextureCube"}, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": true, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.SkinInfo", "_enabled": false, "_blurRadius": 0.01, "_sssIntensity": 3}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null, "_lightProbeSphereVolume": 1}, {"__type__": "cc.PostSettingsInfo", "_toneMappingType": 0}]