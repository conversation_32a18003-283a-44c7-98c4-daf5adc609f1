[{"__type__": "cc.Prefab", "_name": "active_peek", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "active_peek", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 85}, {"__id__": 87}, {"__id__": 89}], "_prefab": {"__id__": 91}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 131072, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "main", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 9}, {"__id__": 13}, {"__id__": 32}, {"__id__": 52}], "_active": true, "_components": [{"__id__": 78}, {"__id__": 80}, {"__id__": 82}], "_prefab": {"__id__": 84}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 131072, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 6}], "_prefab": {"__id__": 8}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 460, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 131072, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 5}, "_contentSize": {"__type__": "cc.Size", "width": 621, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71i4MUaMNFMYYYoC+zbb+0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 7}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9b78bdeb-e32d-4dd6-b66c-dba0ecd1db07@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3dAp3xB2JCsrlR4Qt0sr6J"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "50OVhoHmZAjZMHcT1sHrd/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "barrel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 10}], "_prefab": {"__id__": 12}, "_lpos": {"__type__": "cc.Vec3", "x": -180, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 131072, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 9}, "_enabled": true, "__prefab": {"__id__": 11}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21hWgN5rJCP6tudUgaJabS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "deDrGxfShBvY4ER2EtLEUo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 14}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 13}, "asset": {"__uuid__": "2f65931d-cde4-4384-b955-279c8927f3d3", "__expectedType__": "cc.Prefab"}, "fileId": "57lc+k+ixJpJCfphQW9Jwl", "instance": {"__id__": 15}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "8fbN7RvxJJ5KHfmnUJmtIk", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 16}, {"__id__": 18}, {"__id__": 19}, {"__id__": 20}, {"__id__": 21}, {"__id__": 22}, {"__id__": 24}, {"__id__": 26}, {"__id__": 28}, {"__id__": 30}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 17}, "propertyPath": ["_name"], "value": "time"}, {"__type__": "cc.TargetInfo", "localID": ["57lc+k+ixJpJCfphQW9Jwl"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 17}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 4, "y": -350.293, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 17}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 17}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 17}, "propertyPath": ["_layer"], "value": 131072}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 23}, "propertyPath": ["_layer"], "value": 131072}, {"__type__": "cc.TargetInfo", "localID": ["e1mZeOYG1IXKGy3HAnfC01"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 25}, "propertyPath": ["_layer"], "value": 131072}, {"__type__": "cc.TargetInfo", "localID": ["5555PXCehPaZ4yc9gKf5QV"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 27}, "propertyPath": ["_layer"], "value": 131072}, {"__type__": "cc.TargetInfo", "localID": ["52EYBhBddOjY4dvY20phS1"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 29}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 33.380859375, "height": 39.28}}, {"__type__": "cc.TargetInfo", "localID": ["edIuwAo0VHN6YfJkmprsH9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 31}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "1e3fc4d8-8e38-4856-9742-0297bd9ec056@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["c5NAwKcWVCXKHaoaXrVq+P"]}, {"__type__": "cc.Node", "_name": "btn_scout", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 33}, {"__id__": 39}], "_active": true, "_components": [{"__id__": 45}, {"__id__": 47}, {"__id__": 49}], "_prefab": {"__id__": 51}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -480.614, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 131072, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 32}, "_children": [], "_active": true, "_components": [{"__id__": 34}, {"__id__": 36}], "_prefab": {"__id__": 38}, "_lpos": {"__type__": "cc.Vec3", "x": -82.165, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 131072, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 33}, "_enabled": true, "__prefab": {"__id__": 35}, "_contentSize": {"__type__": "cc.Size", "width": 123, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e7WDfg0OhCMrEoqQYa5AMB"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 33}, "_enabled": true, "__prefab": {"__id__": 37}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "57e2e327-9661-4b1b-b782-6577f5e7882a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f8t2TNL0lNN56FUgqk7szO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d84gN67nBLzZ9eCK4yB/pn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "count", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 32}, "_children": [], "_active": true, "_components": [{"__id__": 40}, {"__id__": 42}], "_prefab": {"__id__": 44}, "_lpos": {"__type__": "cc.Vec3", "x": 68.951, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 131072, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 39}, "_enabled": true, "__prefab": {"__id__": 41}, "_contentSize": {"__type__": "cc.Size", "width": 200.71484375, "height": 73.52}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ec+pGVTf1MnrCuX22Y4RTb"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 39}, "_enabled": true, "__prefab": {"__id__": 43}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "（0/2）", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 52, "_fontSize": 52, "_fontFamily": "Microsoft YaHei", "_lineHeight": 0, "_overflow": 0, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 182, "g": 20, "b": 20, "a": 255}, "_outlineWidth": 4, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "275l28+kJLkq9KOx9NVWgz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d1qwQnv4FONbpV26o67ZWO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 32}, "_enabled": true, "__prefab": {"__id__": 46}, "_contentSize": {"__type__": "cc.Size", "width": 363, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2eoaKgsmhEaLNOXInRZLCP"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 32}, "_enabled": true, "__prefab": {"__id__": 48}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c755959f-43f4-430f-b181-10387143534e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7e7N32K1RACpUqCpyQbjai"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 32}, "_enabled": true, "__prefab": {"__id__": 50}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "30RC6z5DRETJrU2SdrT2og"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "52WiVQ035Eu4T/Ys0W3eff", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_ok", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 53}], "_active": false, "_components": [{"__id__": 71}, {"__id__": 73}, {"__id__": 75}], "_prefab": {"__id__": 77}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -479.815, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 131072, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 52}, "_children": [{"__id__": 54}, {"__id__": 60}], "_active": true, "_components": [{"__id__": 66}, {"__id__": 68}], "_prefab": {"__id__": 70}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 131072, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "ok", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 53}, "_children": [], "_active": true, "_components": [{"__id__": 55}, {"__id__": 57}], "_prefab": {"__id__": 59}, "_lpos": {"__type__": "cc.Vec3", "x": -27.560302734375, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 131072, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 54}, "_enabled": true, "__prefab": {"__id__": 56}, "_contentSize": {"__type__": "cc.Size", "width": 139, "height": 51}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "88yjJDrCVBkpk7eQwDpQqN"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 54}, "_enabled": true, "__prefab": {"__id__": 58}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "af7a3ffb-b872-4574-8393-dd0f832af2ba@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f0jYp0r1JB4YwbbiZR3S3T"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7c0rWJs19P5JpgPOITG0O1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "count", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 53}, "_children": [], "_active": true, "_components": [{"__id__": 61}, {"__id__": 63}], "_prefab": {"__id__": 65}, "_lpos": {"__type__": "cc.Vec3", "x": 69.5, "y": -2.4359999999999786, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 131072, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 62}, "_contentSize": {"__type__": "cc.Size", "width": 55.12060546875, "height": 64.18}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "97OUCUwRBK8rUD7vW/FZcL"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 64}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "2s", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 43, "_fontSize": 43, "_fontFamily": "Microsoft YaHei", "_lineHeight": 0, "_overflow": 0, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 205, "g": 126, "b": 23, "a": 255}, "_outlineWidth": 5, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ca1+Q+WbJAd69ZgJ9BMLw7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fe7vEScxFPCK4z4GPnX5AU", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 67}, "_contentSize": {"__type__": "cc.Size", "width": 194.12060546875, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "abxSJNZX9I+73uqcE1MzDt"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 69}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "88ev5p6RBFR6maCiBVKP97"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "81a8Wqt/VGzZGpg/LF7Cjr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 52}, "_enabled": true, "__prefab": {"__id__": 72}, "_contentSize": {"__type__": "cc.Size", "width": 291, "height": 104}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c7i6AaCA1Jopx7QouxS09T"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 52}, "_enabled": true, "__prefab": {"__id__": 74}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5bdfa879-7e06-492d-95e0-0faaa29af6b8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8qCyxlrxKG7PnDo88XvAO"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 52}, "_enabled": true, "__prefab": {"__id__": 76}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 52}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dfdNwRlVBJyYfZewIhW+18"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7eytaKiK5L07kTymNQzBJs", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 79}, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1325}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bcdLRcu6VJ44zBFOjWVwo8"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 81}, "_alignFlags": 40, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "07u6P6YqdFCau1SuA/jpPw"}, {"__type__": "d067brVOgVCCbVBs2XCJStv", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 83}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e1JkGPPnpN1J1IPYFQCdhY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8eJMYUyvVHPKBnFoZPtCzh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 86}, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "59IZfWwKBAzagxOvuiSpWF"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 88}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6c5gTc121Nobuq0YQ8hrXN"}, {"__type__": "49c3a1l0zlMtZAyECsfjpVp", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 90}, "type": 0, "clip": null, "loop": false, "volume": 1, "playOnAwake": false, "title": {"__id__": 3}, "title_spriteFrame": [{"__uuid__": "9b78bdeb-e32d-4dd6-b66c-dba0ecd1db07@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "21770163-38ec-4655-9a12-643ebc2853d3@f9941", "__expectedType__": "cc.SpriteFrame"}], "btn_ok": {"__id__": 52}, "ok_time_out": {"__id__": 63}, "btn_scout": {"__id__": 32}, "scout_count": {"__id__": 42}, "time_count": null, "main": {"__id__": 2}, "peek_card_item_prefab": {"__uuid__": "e56471ec-95b9-47b2-a10c-caf84fcfd137", "__expectedType__": "cc.Prefab"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bblXKLlbpE6YoAUFdT2Zfo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": [{"__id__": 92}], "nestedPrefabInstanceRoots": [{"__id__": 13}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 89}, "sourceInfo": null, "propertyPath": ["time_count"], "target": {"__id__": 13}, "targetInfo": {"__id__": 93}}, {"__type__": "cc.TargetInfo", "localID": ["66xhH67ntA1av+sEAh+gzK"]}]