import {
    _decorator,
    Button,
    instantiate,
    Label,
    log,
    math,
    Node,
    Prefab,
    Sprite,
    Tween,
    tween,
    UIOpacity,
} from 'cc'
import AudioSourceUILayer from '@/core/manager/gui/layer/AudioSourceUILayer'
import { cat } from '@/core/manager'
import { SocketEvent } from '@/core/business/ws'
import { GameEventConstant } from '@/core/business/constant'
import store from '@/core/business/store'
import {
    audioEffect,
    buttonLock,
    watchUser,
} from '@/core/business/hooks/Decorator'

import { Card } from '@/pb-generate/server/pirate/v1/card_pb'
import { State as DeskState } from '@/pb-generate/server/pirate/v1/state_pb'

import { reflect } from '@bufbuild/protobuf/reflect'
import { create } from '@bufbuild/protobuf'
import GetTimeDifferenceFromServer from '@/core/business/hooks/GetTimeDifferenceFromServer'
import { ZoomState } from '@/script/scene/game/components/barrel/Barrel'
import { sleep } from '@/core/business/util/TimeUtils'
const { ccclass, property } = _decorator

type ActiveRemoveMineProps = {
    /**是否有拆除 */
    is_has_remove: boolean
    /**时间 */
    timeout_sec: number
    closeCallback?: () => void
}
@ccclass('ActiveRemoveMine')
export class ActiveRemoveMine extends AudioSourceUILayer<ActiveRemoveMineProps> {
    @property({ type: Button, tooltip: '逃生按钮' })
    btn_run: Button

    @property({ type: Label, tooltip: '逃生剩余时间' })
    run_time: Label

    @property({ type: Node, tooltip: '警告节点' })
    warning_node: Node

    private timeId: number

    private time_count: number = 3

    override props: ActiveRemoveMineProps = {
        is_has_remove: false,
        timeout_sec: 0,
    }

    override async onLoad(): Promise<void> {
        store.game.layerStatus.revive = true
        await sleep(2500)
        this.close()
    }

    protected override onEventListener(): void {
        cat.event.on(
            GameEventConstant.CLOSE_COMMON_UI,
            this.onCloseCommonUIHandler,
            this
        )
    }

    protected override onAutoObserver(): void {
        this.addAutorun([
            () => {
                if (!this.props.is_has_remove) {
                    cat.util.nodeUtils.setNodeGray(this.btn_run)
                    this.run_time.outlineColor = new math.Color('#585858')
                }

                window.ccLog(
                    'ActiveRemoveMine按钮------------',
                    this.props.is_has_remove && !store.user.isAudience
                )
                // 玩家 且 有拆除牌
                this.btn_run.interactable =
                    this.props.is_has_remove && !store.user.isAudience
            },

            // () => {
            //     if (
            //         store.game.roomData.stateInfo?.state !==
            //         DeskState.CARD_REVIVE
            //     ) {
            //         this.close()
            //     }
            // },
        ])
    }

    override start(): void {
        // this.playAudio()
        // 时间倒计时
        const end_time = GetTimeDifferenceFromServer(this.props.timeout_sec)
        const now = Date.now()
        window.ccLog('时间倒计时', end_time, now - end_time)

        const cb = () => {
            const now = Date.now()
            this.time_count -= 1
            if (now >= end_time) {
                clearInterval(this.timeId)
                this.run_time.string = `0s`
                return
            }
            this.run_time.string = `${this.time_count}s`
        }

        this.timeId = setInterval(cb, 1_000)
        cb()

        this.showWarningTween()
        cat.event.dispatchEvent(GameEventConstant.CLOSE_TEAM_DROP_MINE)
    }

    // 显示警告动画
    showWarningTween() {
        tween(this.warning_node.getComponent(UIOpacity)!)
            .to(0.1, { opacity: 0 })
            .to(0.1, { opacity: 255 })
            .union()
            .repeatForever()
            .start()
    }

    // 隐藏警告动画
    hideWarningTween() {
        Tween.stopAllByTarget(this.warning_node)
    }

    override onDestroy(): void {
        this.hideWarningTween()
        clearInterval(this.timeId)
    }

    protected onCloseCommonUIHandler() {
        this.close()
    }

    private close() {
        this.props?.closeCallback?.()
        store.game.layerStatus.revive = false
        // 关闭界面

        cat.gui.closeUI(this, { isMotion: false })
    }
}
