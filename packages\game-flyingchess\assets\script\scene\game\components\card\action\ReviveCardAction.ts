import {
    _decorator,
    AudioClip,
    AudioSource,
    Component,
    error,
    instantiate,
    log,
    Node,
    Prefab,
} from 'cc'
import { AudioSourceBaseComponent } from '@/core/manager/gui/base/AudioSourceBaseComponent'
import { cat } from '@/core/manager'
import { SocketEvent } from '@/core/business/ws'
import {
    AudioEffectConstant,
    GameEventConstant,
} from '@/core/business/constant'
import { Card } from '@/pb-generate/server/pirate/v1/card_pb'
import { State as DeskState } from '@/pb-generate/server/pirate/v1/state_pb'

import store from '@/core/business/store'
import { ActiveRemoveMine } from '@/script/scene/game/ui/action/active/mine/ActiveRemoveMine'
import { GameInfoBroadcast } from '@/pb-generate/server/pirate/v1/game_pb'
import {
    PlayerDrawnCardMessage,
    PlayerPostedCardBroadcast,
} from '@/pb-generate/server/pirate/v1/player_pb'
import { CommonAction } from './common/CommonAction'

const { ccclass, property } = _decorator

/**
 * <绷带卡牌处理逻辑>
 */
@ccclass('ReviveCardAction')
export class ReviveCardAction extends AudioSourceBaseComponent {
    @property({ type: Prefab, tooltip: '交互-主动-拆除UI-预制体' })
    active_remove_ui_prefab: Prefab = null!

    protected override onEventListener(): void {
        cat.event
            .on<SocketEvent>(
                'MT_PLAYER_POSTED_CARD_BROADCAST',
                this.onGamePostHandler,
                this
            )
            .on<SocketEvent>(
                'MT_PLAYER_DRAWN_CARD_MESSAGE',
                this.onDrawBroadcastHandler,
                this
            )
    }

    protected override onAutoObserver(): void {
        this.addReaction(
            () => store.game.roomData.stateInfo?.state,
            (state) => {
                if (state === DeskState.CARD_REVIVE) {
                    window.ccLog('救援阶段', state === DeskState.CARD_REVIVE)
                    // 播放音效
                    this.getComponent(CommonAction)?.showCardEffect(Card.REVIVE)
                } else {
                }
            },
            {
                fireImmediately: true,
            }
        )
    }

    /**抽牌广播 */
    private async onDrawBroadcastHandler(data: PlayerDrawnCardMessage) {
        if (data.drawnCard == Card.DEATH) {
            this.drawMine(store.game.roomData)
        }
    }

    /**摸雷->拆除地雷-阶段 */
    private drawMine(data: GameInfoBroadcast) {
        // debugger
        // if (data.roundInfo?.currentPlayerCursor === store.user.userIndex) {
        //自己摸雷
        // 显示拆除UI
        const ui_node = instantiate(this.active_remove_ui_prefab)
        cat.gui.openUI<ActiveRemoveMine>(ui_node, {
            props: {
                is_has_remove: true,
                timeout_sec: Number(data.stateInfo?.countdownMs),
                closeCallback: () => {},
            },
        })
        // } else {
        //     //  其他队摸雷不做操作
        // }
    }

    /**出牌广播 */
    private onGamePostHandler(data: PlayerPostedCardBroadcast) {
        // 拆除
        if (data.postedCards[0] == Card.REVIVE) {
            cat.audio.playEffect(AudioEffectConstant.CURE)
        }
    }
}
