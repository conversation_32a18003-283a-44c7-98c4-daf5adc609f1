import { _decorator, Node, sp, isValid } from 'cc'
import { cat } from '@/core/manager'
import { SocketEvent } from '@/core/business/ws'
import { AudioEffectConstant } from '@/core/business/constant'
import {
    Card,
    CardActiveDefenseBroadcast,
    CardStrengthenSuccessBroadcast,
} from '@/pb-generate/server/pirate/v1/card_pb'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { CommonAction } from './common/CommonAction'
import store from '@/core/business/store'
import { sleep } from '@/core/business/util/TimeUtils'

const { ccclass, property } = _decorator

/**
 * <强化 卡牌处理逻辑>
 */
@ccclass('StrengthenCardAction')
export class StrengthenCardAction extends BaseComponent {
    protected override onEventListener(): void {
        cat.event.on<SocketEvent>(
            'MT_CARD_STRENGTHEN_SUCCESS_BROADCAST',
            this.onGamePostHandler,
            this
        )
    }

    /**出牌广播 */
    private async onGamePostHandler(data: CardStrengthenSuccessBroadcast) {
        cat.audio.playEffect(AudioEffectConstant.STRENGTHEN)
        window.ccLog('强化')
        await sleep(200)
        window.ccLog(
            'isPlayerOverStengthLimit: ',
            store.game.isPlayerOverStengthLimit
        )
        if (store.game.isPlayerOverStengthLimit) {
            cat.gui.showToast({
                title: '刀孔数量不足，无法继续增加攻击次数',
            })
        }
        this.getComponent(CommonAction)?.showCardEffect(Card.STRENGTHEN)
    }
}
