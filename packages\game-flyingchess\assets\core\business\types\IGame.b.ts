import { Vec2, v2 } from 'cc'
import { Card } from '@/pb-generate/server/pirate/v1/card_pb'

type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never }

export type XOR<T, U> = (Without<T, U> & U) | (Without<U, T> & T)

/**手牌显示状态 */
export enum HandCardStatus {
    SHOW,
    HIDE,
}

export enum CardInfo {
    DEATH = '弱点',
    REVIVE = '当海贼王即将被刺伤时按照玩家的顺序自动使用保证海贼王存活',
    STRENGTHEN = '玩家在本回合多攻击一次',
    DEFENSE = '当有其他用户对该用户使用指向性道具时触发，使对方的道具无效',
    WEAKEN = '延时效果，在有其他玩家使用强化后，使该强化卡牌无效',
    FREEZE = '使用时指定一名玩家，当轮到该玩家回合时直接跳过',
    PEEK = '使用时指定海盗桶上的两个裂痕，探测裂痕是否能刺伤海贼王',
    STEAL = '使用时指定一名玩家，抽取对方一张道具卡',
    SWAP = '使用时指定一名玩家，交换双方手上的道具卡',
    SUBSTITUTE = '延时效果，当有其他用户对该用户使用指向性道具时触发，使用替身的玩家指定另一个玩家作为指向道具的使用对象',
    DELAY_EFFECT = '延时',
}

export type ExcludedCards = Card.DEATH | Card.UNSPECIFIED

/**对应种类的卡牌信息映射 */
// todo:
export const CardInfoMap: Record<Exclude<Card, ExcludedCards>, CardInfo> = {
    [Card.REVIVE]: CardInfo.REVIVE,
    [Card.STRENGTHEN]: CardInfo.STRENGTHEN,
    [Card.DEFENSE]: CardInfo.DEFENSE,
    [Card.WEAKEN]: CardInfo.WEAKEN,
    [Card.FREEZE]: CardInfo.FREEZE,
    [Card.PEEK]: CardInfo.PEEK,
    [Card.STEAL]: CardInfo.STEAL,
    [Card.SWAP]: CardInfo.SWAP,
    [Card.SUBSTITUTE]: CardInfo.SUBSTITUTE,
    [Card.DELAY_EFFECT]: CardInfo.DELAY_EFFECT,
}

export type Single = 2 | 3 | 4 | 5 | 6

/**对应游戏模式的位置映射 */
export const GameModelSinglePosMap: Record<Single, Vec2[]> = {
    2: [v2(-416, -200), v2(0, 430)],
    3: [v2(-416, -200), v2(-260, 460), v2(260, 460)],
    4: [v2(-416, -200), v2(-300, 420), v2(0, 460), v2(300, 420)],
    5: [
        v2(-416, -200),
        v2(-380, 400),
        v2(-130, 490),
        v2(130, 490),
        v2(380, 390),
    ],
    6: [
        v2(-416, -200),
        v2(-416, 400),
        v2(-220, 490),
        v2(0, 520),
        v2(220, 490),
        v2(416, 400),
    ],
}

export type Team = 2 | 3

export const GameModelTeamPosMap: Record<Team, Vec2[]> = {
    2: [v2(400, -280), v2(-400, -280), v2(-140, 650), v2(112, 650)],
    3: [
        v2(400, -280),
        v2(-400, -280),
        v2(-370, 630),
        v2(-120, 700),
        v2(122, 700),
        v2(370, 630),
    ],
}

/**单人模式 */
export const GameSingle: Record<Single, string> = {
    2: '1V1',
    3: '1V1V1',
    4: '1V1V1V1',
    5: '1V1V1V1V1',
    6: '1V1V1V1V1V1',
}
/**双人模式 */
export const GameTeam: Record<Team, string> = {
    2: '2v2',
    3: '2v2v2',
}

/**卡牌中文名称 */
// todo:
export const CardChineseName: Record<Card, string> = {
    0: '未知牌',
    1: '弱点',
    2: '绷带',
    3: '强化',
    4: '防御',
    5: '虚弱',
    6: '冻结',
    7: '透视',
    8: '索取',
    9: '交换',
    10: '替身',
    11: '延时',
}

export type ServerType = 'Game' | 'Tourist' | null

/**游戏关闭类型 */
export enum GameCloseType {
    /**加入游戏超时 */
    JoinOverTime = 'join_over_time',
    /**结束 */
    GameOver = 'game_over',
}

//care_shark 中，不同的人数对应的上排牙齿数
export const CardDistribution: Record<Single, number> = {
    2: 10,
    3: 12,
    4: 14,
    5: 16,
    6: 18,
}
