import {
    _decorator,
    Camera,
    Component,
    EventTouch,
    geometry,
    log,
    math,
    Node,
    PhysicsSystem,
    Quat,
    v3,
    Vec2,
    Vec3,
} from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { cat } from '@/core/manager'
import { GameEventConstant } from '@/core/business/constant'
import { reflect } from '@bufbuild/protobuf/reflect'
import {
    DataBroadcastRequestSchema,
    DataBroadcastResponseSchema,
} from '@/pb-generate/server/pirate/v1/handler_pb'
import { create } from '@bufbuild/protobuf'

const { ccclass, property } = _decorator

@ccclass('SharkMove')
export class SharkMove extends BaseComponent {
    protected override onLoad(): void {
        this.node.on(Node.EventType.TOUCH_MOVE, this.onTouchMoveHandler, this)
        this.node.on(Node.EventType.TOUCH_END, this.onTouchEndHandler, this)
        this.node.on(Node.EventType.TOUCH_CANCEL, this.onTouchEndHandler, this)
    }

    private onTouchMoveHandler(event: EventTouch) {
        // let uiDelta = event.getUIDelta()
        let delta = event.getDelta()

        cat.event.dispatchEvent(GameEventConstant.ROTATE_BARREL, delta)

        // // 调用移动函数
        // this.rotateShark(delta);
    }

    private onTouchEndHandler(event: EventTouch) {
        let delta = event.getDelta()
        const touch = event.touch!
        if (Vec2.equals(Vec2.ZERO, delta)) {
            window.ccLog('-----点击了')
            cat.event.dispatchEvent(GameEventConstant.CLICK_TOOTH, touch)
        }
    }
}
