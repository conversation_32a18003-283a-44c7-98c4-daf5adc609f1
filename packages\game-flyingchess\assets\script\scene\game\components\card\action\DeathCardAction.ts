import { _decorator, Node, sp, isValid } from 'cc'
import { cat } from '@/core/manager'
import { SocketEvent } from '@/core/business/ws'
import { AudioEffectConstant } from '@/core/business/constant'
import {
    Card,
    CardActiveDefenseBroadcast,
} from '@/pb-generate/server/pirate/v1/card_pb'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { CommonAction } from './common/CommonAction'
import { PlayerDrawnDeathBroadcast } from '@/pb-generate/server/pirate/v1/player_pb'
import { sleep } from '@/core/business/util/TimeUtils'
import store from '@/core/business/store'

const { ccclass, property } = _decorator

/**
 * <弱点 卡牌处理逻辑>
 */
@ccclass('DeathCardAction')
export class DeathCardAction extends BaseComponent {
    protected override onEventListener(): void {
        cat.event.on<SocketEvent>(
            'MT_CARD_DRAWN_DEATH_BROADCAST',
            this.onDrawDeathBroadcastHandler,
            this
        )
    }

    async onDrawDeathBroadcastHandler(data: PlayerDrawnDeathBroadcast) {
        await sleep(1100)
        this.getComponent(CommonAction)?.showCardEffect(Card.DEATH)
        cat.audio.playEffect(
            data.drawnPlayerIndex === store.user.userIndex
                ? AudioEffectConstant.SUCCESS
                : AudioEffectConstant.FAILED
        )
    }
}
