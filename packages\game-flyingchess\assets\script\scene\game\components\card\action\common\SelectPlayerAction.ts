import { _decorator, error, instantiate, log, Node, Prefab } from 'cc'

import { ActiveChoosePlayer } from '@/script/scene/game/ui/action/active/common/choose-player/ActiveChoosePlayer'
import { PlayerItem } from '@/script/scene/game/components/player/PlayerItem'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'

import { cat } from '@/core/manager'
import { SocketEvent } from '@/core/business/ws'
import store from '@/core/business/store'
import { GameEventConstant } from '@/core/business/constant'
import { Arrow } from '@/script/scene/game/components/arrow/Arrow'
import {
    Player,
    PlayerAfterSelectTargetBroadcast,
    PlayerSelectableTargetsMessage,
} from '@/pb-generate/server/pirate/v1/player_pb'
import { GameInfoBroadcast } from '@/pb-generate/server/pirate/v1/game_pb'
import { BaseAction } from './BaseAction'

const { ccclass, property } = _decorator

/**指定玩家 */
@ccclass('SelectPlayerAction')
export abstract class SelectPlayerAction extends BaseAction {
    @property({ type: Prefab, tooltip: '选择玩家UI预制体' })
    choose_player_prefab: Prefab = null!

    @property({ type: Prefab, tooltip: '箭头预制体' })
    arrow_prefab: Prefab

    protected override onEventListener(): void {
        cat.event.on<SocketEvent>(
            'MT_PLAYER_AFTER_SELECT_TARGET_BROADCAST',
            this.onSelectTargetBroadcast,
            this
        )
        cat.event.on<SocketEvent>(
            'MT_PLAYER_SELECTABLE_TARGETS_MESSAGE',
            this.onSelectableTargetsMessage,
            this
        )
    }

    /**指定玩家状态(索取/抛雷/交换) */
    protected onStateGameSelectTarget(
        data: GameInfoBroadcast,
        isSubstitute: boolean = false
    ) {
        window.ccLog(
            `轮到${store.game.selectableTargets.fromPlayerIndex}指定玩家`,
            this.desk.players_node.children.length,
            this.desk.players_node
        )
        const validPlayer = this.checkValidPlayer()
        const showSelectGui =
            store.game.selectableTargets.fromPlayerIndex !== undefined &&
            store.game.selectableTargets.fromPlayerIndex ===
                store.user.userIndex &&
            validPlayer.length > 0
        if (showSelectGui) {
            // 打开选择目标UI界面
            const node = instantiate(this.choose_player_prefab)

            window.ccLog('有效玩家', validPlayer, validPlayer.length)
            // 根据
            cat.gui.openUI<ActiveChoosePlayer>(node, {
                props: { players: validPlayer },
            })
        }
    }

    checkValidPlayer(): Player[] {
        return store.game.playerInfo.players.filter((player) => {
            const { index } = player
            return store.game.selectableTargets.selectablePlayerIndices.includes(
                index
            )
        })
    }

    /**所有人-接收指定请求 */
    protected onSelectTargetBroadcast(data: PlayerAfterSelectTargetBroadcast) {
        console.log('所有人-接收指定请求', data)
        store.game.selectTarget = data
        if (data.targetPlayerIndex == store.user.userIndex) {
            cat.event.dispatchEvent(GameEventConstant.CLOSE_TEAM_DROP_MINE)
        }
        //其他人
        // 其他人 显示指向动画
        const from = store.game.getPlayerComponentByIndex(
            this.desk.players_node,
            data.fromPlayerIndex
        )!.node
        const to = store.game.getPlayerComponentByIndex(
            this.desk.players_node,
            data.targetPlayerIndex
        )!.node
        this.selectTargetTween(from, to)
        // 清理状态
        store.game.clearSelectableTargets()
    }

    protected onSelectableTargetsMessage(data: PlayerSelectableTargetsMessage) {
        store.game.selectableTargets = data
    }

    /**
     * 指定动画
     * @param from 主动发起
     * @param to 被动接受
     */
    selectTargetTween(from: Node, to: Node) {
        if (!from || !to)
            return error(`${!!from || 'from未找到'},${!!to || 'to未找到'}`)
        const arrow = instantiate(this.arrow_prefab)
        arrow
            .getComponent(Arrow)!
            .addToParent(this.desk.card_move_tween, { props: { from, to } })
    }
}
