import {
    _decorator,
    Component,
    instantiate,
    isValid,
    Layers,
    log,
    Node,
    Prefab,
    v3,
    Vec3,
} from 'cc'
import { CommonActionUI } from '../CommonActionUI'
import { cat } from '@/core/manager'
import { GameEventConstant } from '@/core/business/constant'
import { watchUser, buttonLock } from '@/core/business/hooks/Decorator'
import { SocketEvent } from '@/core/business/ws'
import {
    SelectTargetRequestSchema,
    SelectTargetResponseSchema,
} from '@/pb-generate/server/pirate/v1/handler_pb'
import {
    Player,
    PlayerAfterSelectTargetBroadcast,
} from '@/pb-generate/server/pirate/v1/player_pb'
import {
    PlayerItem,
    PlayerState,
} from '@/script/scene/game/components/player/PlayerItem'
import { create } from '@bufbuild/protobuf'
import { reflect } from '@bufbuild/protobuf/reflect'
import store from '@/core/business/store'
import { sleep } from '@/core/business/util/TimeUtils'
const { ccclass, property } = _decorator

export type ActiveChoosePlayerProps = {
    /**玩家节点 */
    players: Player[]
}

@ccclass('ActiveChoosePlayer')
export class ActiveChoosePlayer<
    T extends ActiveChoosePlayerProps = ActiveChoosePlayerProps
> extends CommonActionUI<T> {
    @property({ type: Prefab, tooltip: '玩家预制体' })
    player_prefab: Prefab

    override props: T = {
        players: [],
    } as unknown as T

    protected override onEventListener(): void {
        super.onEventListener()
        cat.event
            .on(
                GameEventConstant.SELECT_PLAYER,
                this.onSelectedPlayerHandler,
                this
            )
            .on<SocketEvent>(
                'MT_PLAYER_AFTER_SELECT_TARGET_BROADCAST',
                this.onSelectTargetBroadcast,
                this
            )
    }

    override start(): void {
        super.start()

        this.props.players.forEach((item, index) => {
            const player_node = instantiate(this.player_prefab)
            const { x, y } = store.game.playersPos.get(item.index)!
            window.ccLog('重新实例玩家', index, item)

            player_node
                .getComponent(PlayerItem)!
                .setNodeAndChildrenLayer(Layers.Enum.UI_2D)
                .setPosition(v3(x, y, 0))
                .addToParent(this.node, {
                    props: { player: item, showCurseCountImmediately: true },
                    data: { state: PlayerState.NORMAL, show_select: true },
                })
        })

        // this.props.players.forEach(item => {
        //     window.ccLog(item.getComponent(PlayerItem)!)
        //     // debugger
        //     item.setParent(this.node, true)
        // item.setPosition(Vec3.ZERO)

        // item.setScale(v3(2, 2, 2))
        // console.log(item.position.x, item.position.y)
        // item.getComponent(PlayerItem)!.showSelect()
        // })
    }

    protected override async close() {
        // this.props.players.forEach(item => {
        //     if (isValid(item)) {
        //         item.getComponent(PlayerItem)!.hideSelect()
        //         item.parent = this.props.originNode
        //     }
        // })

        await sleep(300)
        super.close()
    }

    /**选择玩家处理 */
    @watchUser()
    @buttonLock()
    onSelectedPlayerHandler(player: Player): void {
        cat.ws
            .Request(
                'SelectTarget',
                reflect(
                    SelectTargetRequestSchema,
                    create(SelectTargetRequestSchema, {
                        targetPlayerIndex: player.index,
                    })
                ),
                SelectTargetResponseSchema
            )
            .then(() => {
                this.close()
            })
    }

    /**系统自动指定 */
    protected async onSelectTargetBroadcast(
        data: PlayerAfterSelectTargetBroadcast
    ) {
        // 关闭主动指定UI
        this.node.children.forEach((item) => {
            if (
                item.getComponent(PlayerItem) &&
                item.getComponent(PlayerItem)?.props.player.index ==
                    data.targetPlayerIndex
            ) {
                item.getComponent(PlayerItem)!.setUpdateData({
                    is_selected: true,
                })
            }
        })
        this.close()
    }
}
