import {
    _decorator,
    Component,
    misc,
    MotionStreak,
    Node,
    Sprite,
    Tween,
    tween,
    UITransform,
    Vec3,
} from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
const { ccclass, property } = _decorator

type ArrowProps = {
    /**起点 */
    from: Node
    /**终点 */
    to: Node
}

@ccclass('Arrow')
export class Arrow extends BaseComponent<ArrowProps> {
    protected override start(): void {
        const { from, to } = this.props

        // 计算角度
        const angle = Math.atan2(
            to.worldPosition.y - from.worldPosition.y,
            to.worldPosition.x - from.worldPosition.x
        )

        // 获取起点终点相对于game_zone节点位置
        const fromPosition = from
            .parent!.getComponent(UITransform)!
            .convertToNodeSpaceAR(from.worldPosition)
        const toPosition = to
            .parent!.getComponent(UITransform)!
            .convertToNodeSpaceAR(to.worldPosition)

        this.node.setPosition(fromPosition)
        this.node.angle = misc.radiansToDegrees(angle)

        tween(this.node)
            .to(0.5, { position: toPosition })
            .delay(0.5)
            .call(() => {
                this.node.destroy()
            })
            .start()
    }
}
