import {
    _decorator,
    Camera,
    Component,
    EventTouch,
    Input,
    input,
    isValid,
    log,
    math,
    Node,
    PhysicsSystem,
    Quat,
    Sprite,
    tween,
    v3,
    Vec2,
    Vec3,
    Touch,
    geometry,
    Material,
    SkeletalAnimation,
    v2,
    Animation,
    Tween,
    sp,
    view,
    Prefab,
    resources,
    instantiate,
    MeshRenderer,
    lerp,
} from 'cc'
import { CardState } from '@/pb-generate/server/pirate/v1/card_pb'
import {
    DataBroadcastRequest,
    DataBroadcastRequestSchema,
    DataBroadcastResponseSchema,
    // DeskState,
    // UiAction,
    // UiBroadcast,
} from '@/pb-generate/server/pirate/v1/handler_pb'
import { cat } from '@/core/manager'
import {
    AudioEffectConstant,
    AudioEventConstant,
    GameEventConstant,
} from '@/core/business/constant'
import store from '@/core/business/store'
import { CardDistribution, Single } from '@/core/business/types/IGame'
import { KnifeHole } from './KnifeHole'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { SocketEvent } from '@/core/business/ws'
import { DataBroadcastContent } from '@/pb-generate/client/game/game_pb'
import { State as DeskState } from '@/pb-generate/server/pirate/v1/state_pb'
import { create, fromBinary } from '@bufbuild/protobuf'
import { reflect } from '@bufbuild/protobuf/reflect'
import { watchUser } from '@/core/business/hooks/Decorator'
import { ClickToothType } from '@/core/business/store/game'
import { ClientBootParamSchema } from 'sgc'
import {
    ClientDataBroadcastSchema,
    ServerDataBroadcast,
    ServerDataBroadcast_Action,
    ServerDataBroadcastSchema,
    ServerSelectedDataSchema,
} from '@/pb-generate/server/pirate/v1/game_pb'
import { BasePrefab } from '@/core/manager/gui'
import { BasePool } from '@/core/manager/gui/base/BasePool'

const { ccclass, property } = _decorator

type BarrelProps = {
    card_state: CardState[]
}

export enum ZoomState {
    FAR,
    NEAR,
}

type sharkTeethEulerYMapType = Record<number, number>

type BarrelData = {
    barrelAngle: number
    isNear: boolean
    isCameraZooming: boolean
}

@ccclass('Barrel')
export class Barrel extends BaseComponent<BarrelProps, BarrelData> {
    @property({ type: Camera, tooltip: '3D摄像机' })
    camera3D: Camera = null!

    @property({ type: Camera, tooltip: '2D摄像机' })
    camera2D: Camera = null!

    @property({ type: Node, tooltip: '基准2D节点' })
    base2D: Node = null!

    @property({ type: Node, tooltip: '木桶3D信息节点' })
    barrel3D: Node = null!

    @property({ type: Node, tooltip: '木桶模型(旋转)节点' })
    barrel_rotate: Node = null!

    @property({ type: Node, tooltip: '(咬合动效)木桶模型节点' })
    shark_bite: Node = null!

    @property({ type: Node, tooltip: '(牙齿交互)木桶模型节点' })
    shark_teeth: Node = null!

    @property({ type: Node, tooltip: '鲨鱼咬人动画容器节点' })
    bite_tween_node: Node = null!

    @property({ type: sp.Skeleton, tooltip: '鲨鱼咬人骨骼动画节点' })
    bite_skeleton_node: sp.Skeleton = null!

    @property({ type: [Node] })
    top_teeth: Node[] = []

    @property({ type: [Node] })
    bot_teeth: Node[] = []

    @property({ type: [Material] })
    colorMaterials: Material[] = []

    @property({ type: Prefab, tooltip: '缝隙预制体' })
    knife_hole_item_prefab: Prefab = null!

    override data: BarrelData = {
        barrelAngle: 0,
        isNear: false,
        isCameraZooming: false,
    }

    //缝隙池子
    public knifeHolePool: BasePool | null = null

    /**摄像机状态 */
    camera3DState: ZoomState = ZoomState.NEAR

    /**射线 */
    private ray: geometry.Ray = new geometry.Ray()

    private barreleOriginPosition: Vec3 = new Vec3()
    private barreleFarRotation: Quat = Quat.fromEuler(new Quat(), 34, 0, 0)
    private barreleNearRotation: Quat = Quat.fromEuler(new Quat(), 24, 0, 0)

    override props: BarrelProps = {
        card_state: [],
    }

    /**有效节点 */
    private validTeeth: Node[] = []

    /**无效节点 */
    private invalidTeeth: Node[] = []

    /**安全区域顶部 */
    safeTop: number = 0
    /**安全区域底部 */
    safeBottom: number = 0

    override onLoad() {
        // if (store.global.customPlatform == CustomPlatform.DaiDaiH5) {
        // 通过URL获取安全区域
        const url_params = cat.util.stringUtil.getURLParameters(
            decodeURIComponent(window.location.href)
        )

        this.safeTop = Number(url_params.safe_top || 0)
        this.safeBottom = Number(url_params.safe_bottom || 0)
        // globalThis.onAiClickToothPre = this.onAiClickToothPre.bind(this)
        this.knifeHolePool = new BasePool(this.knife_hole_item_prefab)
        this.barreleOriginPosition = this.barrel3D.getPosition()
    }

    initTooth(cards?: CardState[]) {
        window.ccLog('initTooth: ', cards)
        if (cards) {
            this.validTeeth = []
            this.invalidTeeth = []
            store.game.player_teeth = cards
            const distribution = Math.ceil(cards.length / 2)

            const top = cards.slice(0, distribution)
            const bot = cards.slice(distribution)

            this.generateKnifeHoles(distribution)
            this.generateKnifeHoles(cards.length - distribution, false)
            this.topValidTooth(top)
            this.botValidTooth(bot)

            // 有效牙齿标记初始状态
            this.validTeeth.forEach((item, index) => {
                item.getComponent(KnifeHole)?.setUpdateProps({
                    state: cards[index],
                    index,
                    colorMaterials: this.colorMaterials,
                })
            })
        }
    }

    private topValidTooth(top: CardState[]) {
        let empty = this.top_teeth.length - top.length
        let all = this.top_teeth
        if (empty > 0) {
            this.invalidTeeth.push(...all.slice(-empty))

            // 无效牙齿标记已使用
            this.invalidTeeth.forEach((item) => {
                item.active = false
            })
        } else if (empty < 0) {
            window.ccLog('topValidTooth: 上部的缝隙不够', empty)
        }

        // 获取有效牙齿
        this.top_teeth.forEach((item) => {
            if (!this.invalidTeeth.includes(item)) {
                window.ccLog('有效')
                this.validTeeth.push(item)
            }
        })
    }

    private botValidTooth(bot: CardState[]) {
        let empty = this.bot_teeth.length - bot.length
        let all = this.bot_teeth

        //window.ccLog('botValidTooth: empty number:', empty)
        if (empty > 0) {
            this.invalidTeeth.push(...all.slice(-empty))

            // 无效牙齿标记已使用
            this.invalidTeeth.forEach((item) => {
                item.active = false
            })
        } else if (empty < 0) {
            window.ccLog('botValidTooth: 下部的缝隙不够', empty)
        }

        this.bot_teeth.forEach((item) => {
            if (!this.invalidTeeth.includes(item)) {
                this.validTeeth.push(item)
            }
        })
    }

    protected override onEventListener(): void {
        cat.event
            .on(GameEventConstant.ROTATE_BARREL, this.rotationShark, this)
            .on(GameEventConstant.CLICK_TOOTH, this.clickTooth, this)
            .on(GameEventConstant.BARREL_ZOOM, this.onSharkZoomHandler, this)
            .on(
                GameEventConstant.MOCK_SHARK_ROTATE,
                this.onMockSharkRotate,
                this
            )

            .on<SocketEvent>(
                'MT_CLIENT_DATA_BROADCAST',
                this.onDataBroadcastHandler,
                this
            )
            .on<SocketEvent>(
                'MT_SERVER_DATA_BROADCAST',
                this.onEventUIBroadcast,
                this
            )
    }

    private onEventUIBroadcast(event: ServerDataBroadcast) {
        switch (event.action) {
            case ServerDataBroadcast_Action.DRAW_SELECTED:
            case ServerDataBroadcast_Action.PEEK_SELECTED:
                return this.scheduleOnce(() => {
                    this.onAiClickToothPre(
                        fromBinary(ServerSelectedDataSchema, event.data)
                            .cardIndices
                    )
                }, 1)
        }
    }

    protected override onAutoObserver(): void {
        this.addAutorun([
            () => {
                if (this.data.isCameraZooming === true) {
                    return
                }
                const axis = v3(0, 1, 0) // 旋转轴
                // 获取当前的旋转四元数

                const shark_quat_nonce = new Quat()
                // 使用 quat.rotateAround 计算新的四元数
                Quat.rotateAroundLocal(
                    shark_quat_nonce,
                    Quat.fromEuler(new Quat(), 0, 0, 0),
                    axis,
                    this.data.barrelAngle
                ) // 生成新的旋转四元数

                // 转换为欧拉角
                const euler = v3()
                Quat.toEuler(euler, shark_quat_nonce)

                this.setSharkRotate(euler)
            },
        ])

        this.addReaction(
            () => this.props.card_state,
            (card_state) => {
                this.validTeeth.forEach((item, index) => {
                    item.getComponent(KnifeHole)?.setUpdateProps({
                        state: card_state[index],
                        colorMaterials: this.colorMaterials,
                    })
                })
            },
            { fireImmediately: true }
        )
            .addReaction(
                () => store.game.is_bite,
                (is_bite) => {
                    // this.shark_teeth.active = !(this.shark_bite.active = is_bite)
                    window.ccLog('-------------is_bite', is_bite)
                    if (is_bite) {
                        this.playBiteTwween()
                        const shark_bite =
                            this.shark_bite.getComponent(SkeletalAnimation)!
                        const clips = shark_bite.clips
                        const first_clip = clips[0]
                        if (first_clip) {
                            shark_bite.getState(first_clip.name).wrapMode = 1
                            shark_bite.on(
                                Animation.EventType.FINISHED,
                                this.onAnimationFinished,
                                this
                            )
                            shark_bite.getState(first_clip.name).play()
                        }
                    }
                },
                { fireImmediately: true }
            )
            .addReaction(
                () => store.game.isBarrelZoomNear,
                (isBarrelZoomNear) => {
                    isBarrelZoomNear
                        ? this.zoomNearCamera()
                        : this.zoomFarCamera()
                }
            )
    }

    // 播放鲨鱼咬人（星星闪烁）动效
    private playBiteTwween() {
        this.bite_tween_node.setPosition(
            this.camera3D.convertToUINode(
                this.bot_teeth[8].getWorldPosition(),
                this.bite_tween_node.parent!
            )
        )
        const cb = () => {
            if (isValid(this.bite_skeleton_node.node)) {
                this.bite_skeleton_node.node.active = false
            }
        }
        this.bite_skeleton_node.setCompleteListener(cb)
        this.bite_skeleton_node.node.active = true
        this.bite_skeleton_node.setAnimation(0, 'idle', false)
    }

    /**放大(拉进摄像机) */
    private zoomNearCamera() {
        // if (this.camera3D.priority === 100) return
        this.data.isCameraZooming = true
        const centerWorldPostion2D = v3(
            this.base2D.worldPosition.x,
            this.base2D.worldPosition.y - 160,
            0
        )
        let worldToScreen = v3()
        this.camera2D.camera.update(true)
        this.camera2D.camera.worldToScreen(worldToScreen, centerWorldPostion2D)

        this.camera3D.camera.update(true)
        const ray = this.camera3D.camera.screenPointToRay(
            new geometry.Ray(),
            worldToScreen.x,
            worldToScreen.y
        )

        let hit = new Vec3()
        ray.computeHit(hit, 8)

        this.camera3D.priority = 100
        const position = v3(0, hit.y, hit.z)
        Tween.stopAllByTarget(this.barrel3D)

        tween(this.barrel3D)
            .to(0.2, { position, rotation: this.barreleNearRotation })
            .call(() => {
                this.data.isCameraZooming = false
                this.data.isNear = true
                // this.data.barrelAngle = 0
            })
            .start()
    }

    /**恢复摄像机位置 */
    private zoomFarCamera() {
        this.data.isCameraZooming = true
        // this.camera3D.camera.node.setPosition(this.camera3D_init_position)
        // 3D v3(-0.62,0.717,-10) r v3(23.9,0,-1.406)
        // if (this.camera3D.priority === 1) return
        const rotation = this.barreleFarRotation
        // this.removeEventMove()
        Tween.stopAllByTarget(this.barrel3D)
        tween(this.barrel3D)
            .to(0.2, { position: this.barreleOriginPosition, rotation })
            .call(() => {
                store.game.is_bite = false
                this.camera3D.priority = 1
                this.data.isCameraZooming = false
                this.data.isNear = false
                // this.data.barrelAngle = 0
            })
            .start()
    }

    private onDataBroadcastHandler(data: DataBroadcastRequest) {
        const nocne = JSON.parse(data.data) as unknown as DataBroadcastContent
        const { barrelAngle, selected, index } = nocne
        if (barrelAngle && index !== store.game.playerInfo?.playerIndex) {
            // const delta = v2(rotate.x, rotate.y);
            // this.rotationShark(delta)
            this.data.barrelAngle = barrelAngle
        }

        if (selected && index !== store.game.playerInfo?.playerIndex) {
            ;[...this.top_teeth, ...this.bot_teeth].forEach((item, i) => {
                if (selected.includes(i)) {
                    item.getComponent(KnifeHole)?.selected()
                } else {
                    item.getComponent(KnifeHole)?.unselected()
                }
            })
        }
    }

    /**旋转鲨鱼 */
    private rotationShark(delta: Vec2 = Vec2.ZERO) {
        // 计算旋转角度
        const rad = delta.length() * 0.01 // 调整比例因子以控制旋转灵敏度

        this.data.barrelAngle =
            delta.x >= 0
                ? this.data.barrelAngle + rad
                : this.data.barrelAngle - rad

        // 同步
        cat.ws.Request(
            'DataBroadcast',
            reflect(
                ClientDataBroadcastSchema,
                create(ClientDataBroadcastSchema, {
                    data: JSON.stringify({
                        barrelAngle: this.data.barrelAngle,
                        index: store.game.playerInfo?.playerIndex,
                    }),
                })
            ),
            ClientDataBroadcastSchema
        )
    }

    private onSharkZoomHandler(zoom: ZoomState) {
        window.ccLog('onSharkZoomHandler：', zoom)

        if (zoom === ZoomState.FAR) {
            this.zoomFarCamera()
        } else {
            this.zoomNearCamera()
        }
    }

    /**点击牙齿 */
    private clickTooth(touch: Touch) {
        this.camera3D.screenPointToRay(
            touch.getLocationX(),
            touch.getLocationY(),
            this.ray
        )
        if (PhysicsSystem.instance.sweepSphereClosest(this.ray, 0.001)) {
            const raycastResults = PhysicsSystem.instance.sweepCastClosestResult
            window.ccLog(
                '--------------射线检测',
                raycastResults.collider.node.parent?.name
            )

            if (raycastResults.collider.node.parent) {
                const collider_node = raycastResults.collider.node.parent //knife_hole node
                const knifeHole = collider_node?.getComponent(KnifeHole)

                // 检查牙齿是否在桶的正面（面向摄像机）
                if (!this.isToothFacingCamera(collider_node)) {
                    window.ccLog('牙齿在背面，忽略点击')
                    return
                }

                if (
                    !store.game.selected_tooth.includes(
                        raycastResults.collider.node.parent
                    )
                ) {
                    if (
                        collider_node &&
                        knifeHole &&
                        ![CardState.USED].includes(knifeHole.props.state!)
                    ) {
                        cat.audio.playEffect(AudioEffectConstant.CLICK)
                        store.game.selected_tooth.push(collider_node)
                    }
                } else {
                    store.game.selected_tooth =
                        store.game.selected_tooth.filter(
                            (item) => item !== collider_node
                        )
                }
            }

            switch (store.game.click_tooth_type) {
                case ClickToothType.DRAW:
                    store.game.selected_tooth = store.game.selected_tooth.slice(
                        -store.game.getPlayerDrawCount
                    )
                    break
                case ClickToothType.SCOUT:
                    store.game.selected_tooth =
                        store.game.selected_tooth.slice(-2)
                    break

                default:
                    break
            }

            const selected: number[] = []
            ;[...this.top_teeth, ...this.bot_teeth].forEach((item, index) => {
                if (store.game.selected_tooth.includes(item)) {
                    window.ccLog('selected', item.name, ',index:', index)
                    item.getComponent(KnifeHole)?.selected()
                    selected.push(index)
                } else {
                    item.getComponent(KnifeHole)?.unselected()
                }
            })

            // 添加控制条件
            if (selected && store.game.is_allow_click_tooth) {
                cat.ws.Request(
                    'DataBroadcast',
                    reflect(
                        ClientDataBroadcastSchema,
                        create(ClientDataBroadcastSchema, {
                            data: JSON.stringify({
                                selected,
                                index: store.game.playerInfo?.playerIndex,
                            }),
                        })
                    ),
                    ClientDataBroadcastSchema
                )
            }
        } else {
            console.log('raycast does not hit the target node !')
        }
    }

    /**咬合 */
    playBite() {
        store.game.is_bite = true
    }

    private onAnimationFinished() {
        this.scheduleOnce(() => {
            if (this.camera3D.priority == 1) {
                store.game.is_bite = false
            } else {
            }
        }, 1)
    }

    /**将鲨鱼设置到中间 */
    // setSharkRotateCenter() {
    //     //window.ccLog('Barrel-->setSharkRotateCenter()')
    //     tween(this.data)
    //         .to(0.2, {
    //             barrelAngle: {
    //                 value: 0,
    //                 progress: (start, end, current, t) => {
    //                     return lerp(start, end, t)
    //                 },
    //             },
    //         })
    //         .start()
    // }

    /**设置鲨鱼旋转 */
    @watchUser()
    setSharkRotate(euler: Vec3 = Vec3.ZERO) {
        //window.ccLog('Barrel-->setSharkRotate(), euler', euler)
        // 将限制后的欧拉角转换回四元数
        const rotate = Quat.fromEuler(new Quat(), euler.x, euler.y, euler.z)
        this.barrel_rotate.setRotation(rotate)

        //同步到其它用户时设置为0
        // rotate.x = 0
        // rotate.z = 0
    }

    private async onAiClickToothPre(toothIndexList: number[]) {
        for (let toothIndex of toothIndexList) {
            await this.mockSharkRotate(toothIndex)
            await this.mockToothSelected(toothIndex)
        }
    }

    /**
     * 处理MOCK_SHARK_ROTATE事件
     * @param params 事件参数，包含toothIndex和duration
     */
    private onMockSharkRotate(params: {
        toothIndex: number
        duration?: number
    }) {
        const { toothIndex, duration = 0.2 } = params
        return this.mockSharkRotate(toothIndex, duration)
    }

    /**
     * 模拟旋转桶到指定牙齿位置
     * @param toothIndex 牙齿索引
     * @param duration 动画持续时间
     */
    private mockSharkRotate(toothIndex: number, duration = 0.2) {
        const targetTooth = this.validTeeth[toothIndex]
        if (!targetTooth) {
            window.ccLog('mockSharkRotate: 找不到目标牙齿', toothIndex)
            return Promise.resolve()
        }

        const knifeHole = targetTooth.getComponent(KnifeHole)!
        let targetAngle = knifeHole.props.angle || 0

        // 判断是否为下排牙齿
        const isBottomTooth = targetTooth.name.startsWith('bot_hole_')

        // 如果是下排牙齿，需要重新计算targetAngle
        if (isBottomTooth) {
            // 对于下排牙齿，需要调整角度计算
            // 下排牙齿的角度需要取反并加上π（180度）
            targetAngle = -targetAngle + Math.PI
            window.ccLog('mockSharkRotate: 下排牙齿角度调整', targetAngle)
        }

        // 计算角度差的绝对值
        let angleDiff = Math.abs(targetAngle - this.data.barrelAngle)

        // 如果角度差大于π，通过加减2π使targetAngle向barrelAngle逼近
        // 使用循环处理，直到角度差小于或等于π
        while (angleDiff > Math.PI) {
            if (targetAngle > this.data.barrelAngle) {
                targetAngle -= 2 * Math.PI
            } else {
                targetAngle += 2 * Math.PI
            }
            // 重新计算角度差
            angleDiff = Math.abs(targetAngle - this.data.barrelAngle)
        }

        window.ccLog(
            'mockSharkRotate: 调整后的目标角度',
            targetAngle,
            '当前角度',
            this.data.barrelAngle,
            '角度差',
            angleDiff
        )

        return new Promise<void>((resolve) => {
            tween(this.data)
                .set({
                    barrelAngle: this.data.barrelAngle,
                })
                .to(duration, {
                    barrelAngle: {
                        value: targetAngle,
                        progress: (start, end, _current, t) => {
                            return lerp(start, end, t)
                        },
                    },
                })
                .call(() => {
                    resolve()
                })
                .start()
        })
    }

    private mockToothSelected(toothIndex: number, duration = 0.2) {
        return new Promise<void>((resolve) => {
            this.scheduleOnce(() => {
                this.validTeeth.forEach((item, i) => {
                    if (toothIndex === i) {
                        item.getComponent(KnifeHole)?.selected()
                    }
                })
                resolve()
            }, duration)
        })
    }

    /**
     * 生成缝隙
     * @param count 缝隙数量
     * @param isTop 是否在上部
     */
    generateKnifeHoles(count: number, isTop: boolean = true) {
        const yPosition = isTop ? 0.02 : 0.134 // 缝隙所在垂直方向的位置

        // 计算每个缝隙之间的角度
        const angleStep = (2 * Math.PI) / count

        // 初始角度从0开始
        let startAngle = 0

        for (let i = 0; i < count; i++) {
            const theta = startAngle - i * angleStep // 当前角度（弧度）
            const newNode = this.knifeHolePool!.get()!

            if (isTop) {
                newNode.name = `top_hole_${i}`
            } else {
                newNode.name = `bot_hole_${i}`
            }
            newNode.setPosition(v3(0, yPosition, 0))

            // 计算切线方向
            const tangentRotation = this.calculateTangentRotation({
                theta,
                isTop,
            })
            newNode.setRotation(tangentRotation)

            // 设置缝隙的角度属性
            const knifeHole = newNode.getComponent(KnifeHole)!
            knifeHole.setOptions({
                props: {
                    angle: theta,
                    colorMaterials: this.colorMaterials,
                },
            })

            this.barrel_rotate.addChild(newNode) // 添加至木桶节点

            if (isTop) {
                this.top_teeth.push(newNode)
            } else {
                this.bot_teeth.push(newNode)
            }
        }
    }
    calculateTangentRotation({
        theta,
        isTop,
    }: {
        theta: number
        isTop: boolean
    }) {
        const axis = v3(0, isTop ? 1 : -1, 0) // 旋转轴

        const quat_nonce = new Quat()
        // 使用 quat.rotateAround 计算新的四元数
        Quat.rotateAroundLocal(quat_nonce, new Quat(), axis, -theta) // 生成新的旋转四元数

        // 转换为欧拉角
        const euler = v3()
        Quat.toEuler(euler, quat_nonce)
        const rotation = Quat.fromEuler(
            new Quat(),
            euler.x + (isTop ? 0 : 180),
            euler.y,
            euler.z
        )
        return rotation
    }

    /**
     * 检查牙齿是否面向摄像机（在桶的正面）
     * @param toothNode 牙齿节点
     * @returns 如果牙齿面向摄像机返回 true，否则返回 false
     */
    private isToothFacingCamera(toothNode: Node): boolean {
        // 1. 获取牙齿的角度
        const knifeHole = toothNode.getComponent(KnifeHole)
        if (!knifeHole || knifeHole.props.angle === undefined) {
            return false
        }

        // 牙齿的初始角度（弧度）
        const toothAngleRadians = knifeHole.props.angle

        // 2. 获取桶子当前的旋转角度（弧度）
        const barrelAngleRadians = this.data.barrelAngle

        // 3. 判断牙齿是上排还是下排
        const isTopTooth = toothNode.name.startsWith('top_hole_')

        // 4. 计算牙齿相对于摄像机的角度（弧度）
        // 注意：上排和下排牙齿的旋转轴和初始旋转都不同
        // 上排牙齿：相对角度 = 牙齿的初始角度 - 桶子的旋转角度
        // 下排牙齿：相对角度 = 牙齿的初始角度 + 桶子的旋转角度 + Math.PI
        let relativeAngleRadians = isTopTooth
            ? toothAngleRadians - barrelAngleRadians
            : toothAngleRadians + barrelAngleRadians + Math.PI

        // 5. 将弧度标准化到 -PI 到 PI 范围
        while (relativeAngleRadians > Math.PI) {
            relativeAngleRadians -= 2 * Math.PI
        }
        while (relativeAngleRadians < -Math.PI) {
            relativeAngleRadians += 2 * Math.PI
        }

        // 6. 计算牙齿相对于摄像机的角度（度）
        const relativeAngleDegrees = (relativeAngleRadians * 180) / Math.PI

        // 7. 取绝对值，判断牙齿是否在桶的正面
        const absoluteAngleDegrees = Math.abs(relativeAngleDegrees)

        // 8. 判断牙齿是否在桶的正面
        const isFacingCamera = absoluteAngleDegrees < (isTopTooth ? 91 : 90)

        window.ccLog(
            `牙齿 ${toothNode.name}, ` +
                `类型: ${isTopTooth ? '上排' : '下排'}, ` +
                `桶旋转角度: ${((barrelAngleRadians * 180) / Math.PI).toFixed(
                    2
                )}°, ` +
                `牙齿初始角度: ${((toothAngleRadians * 180) / Math.PI).toFixed(
                    2
                )}°, ` +
                `相对角度: ${relativeAngleDegrees.toFixed(2)}°, ` +
                `绝对角度: ${absoluteAngleDegrees.toFixed(2)}°, ` +
                `是否可见: ${isFacingCamera}`
        )

        return isFacingCamera
    }
}
