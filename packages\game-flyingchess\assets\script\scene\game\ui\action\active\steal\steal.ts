import { _decorator, Button, Component, Label, Layers, Node } from 'cc'
import { GameEventConstant } from '@/core/business/constant'
import {
    watchUser,
    audioEffect,
    buttonLock,
} from '@/core/business/hooks/Decorator'
import { SocketEvent } from '@/core/business/ws'
import { BaseHandCard } from '@/script/scene/game/components/desk/BaseHandCard'
import { CommonActionUI } from '../../active/common/CommonActionUI'
import { cat } from '@/core/manager'
import { reflect } from '@bufbuild/protobuf/reflect'
// import {
//     UiAction,
//     UiBroadcast,
// } from '@/pb-generate/server/pirate/v1/message_pb'

import store from '@/core/business/store'
import { create, fromBinary } from '@bufbuild/protobuf'
import { DataBroadcastContent } from '@/pb-generate/client/game/game_pb'
import { Player } from '@/pb-generate/server/pirate/v1/player_pb'
import {
    DataBroadcastRequest,
    StealCardRequestSchema,
    StealCardResponseSchema,
} from '@/pb-generate/server/pirate/v1/handler_pb'
import {
    ClientDataBroadcast,
    ClientDataBroadcastSchema,
    ServerDataBroadcast,
    ServerDataBroadcast_Action,
    ServerSelectedDataSchema,
} from '@/pb-generate/server/pirate/v1/game_pb'
import { Desk } from '@/script/scene/game/components/desk/Desk'

const { ccclass, property } = _decorator

type StealProps = {
    /**桌面组件 */
    desk: Desk
    /**发起人 */
    requestPlayer: Player
    // 被索取者
    responsePlayer: Player
    // 是否是发起者
    isRequestPlayer: boolean
}

@ccclass('Steal')
export class Steal extends CommonActionUI<StealProps> {
    @property({ type: Node, tooltip: '给牌按钮' })
    btn_give_card: Node = null!

    @property({ type: Label, tooltip: '索要提示' })
    request_tips: Label = null!

    @property({ type: BaseHandCard, tooltip: '手牌节点' })
    hand_card: BaseHandCard = null!

    override onLoad(): void {
        this.btn_give_card.on(
            Button.EventType.CLICK,
            this.onGiveCardHandler,
            this
        )
    }

    override start(): void {
        this.request_tips.string = this.props.isRequestPlayer
            ? `你向${this.props.responsePlayer.nickname}索要,请选择一张牌`
            : `${this.props.requestPlayer.nickname}向你索要`

        // cat.event.dispatchEvent(GameEventConstant.HAND_CARD_STATE, false)
        if (!this.props.isRequestPlayer) {
            this.btn_give_card.removeFromParent()

            this.hand_card
                .onceAddCard(store.game.playerCards)
                .setNodeAndChildrenLayer(Layers.Enum.DEFAULT)
                .setHandCardGray(true)
        } else {
            for (let i = 0; i < this.props.responsePlayer.handCardCount; i++) {
                this.hand_card.onceAddCard([0])
            }
            this.hand_card.setNodeAndChildrenLayer('UI_IN_SCENE')
            // .setHandCardGray(true)
        }
    }

    protected override onEventListener(): void {
        cat.event
            .on<SocketEvent>(
                'MT_CARD_AFTER_STEAL_MESSAGE',
                this.onResponseRequestCardBroadcast,
                this
            )
            .on<SocketEvent>(
                'MT_CLIENT_DATA_BROADCAST',
                this.onDataBroadcastHandler,
                this
            )
            .on<SocketEvent>(
                'MT_SERVER_DATA_BROADCAST',
                this.onEventUIBroadcast,
                this
            )

        // .on<SocketEvent>(
        //     'EVENT_UI_BROADCAST',
        //     this.onEventUIBroadcast,
        //     this
        // )
    }

    @watchUser()
    @audioEffect()
    @buttonLock()
    private onGiveCardHandler() {
        // 获取选中的手牌
        const stealSelectedCardIndex = store.game.stealSelectedCardIndex
        if (stealSelectedCardIndex === undefined) {
            return cat.gui.showToast({ title: '请选择一张手牌' })
        }

        cat.ws
            .Request(
                'StealCard',
                reflect(
                    StealCardRequestSchema,
                    create(StealCardRequestSchema, {
                        targetHandCardIndices: [stealSelectedCardIndex],
                        targetPlayerIndex:
                            store.game.selectTarget.targetPlayerIndex,
                    })
                ),
                StealCardResponseSchema
            )
            .then(() => {
                store.game.stealSelectedCardIndex = undefined
            })
    }

    /**索取响应广播 */
    private onResponseRequestCardBroadcast() {
        store.game.stealSelectedCardIndex = undefined
        this.close()
    }

    private onDataBroadcastHandler(data: DataBroadcastRequest) {
        const nocne = JSON.parse(data.data) as unknown as DataBroadcastContent
        const { selectedCardIndex } = nocne
        store.game.stealSelectedCardIndex = selectedCardIndex
    }

    private onEventUIBroadcast(event: ServerDataBroadcast) {
        switch (event.action) {
            case ServerDataBroadcast_Action.STEAL_SELECTED:
                return this.scheduleOnce(() => {
                    store.game.stealSelectedCardIndex = fromBinary(
                        ServerSelectedDataSchema,
                        event.data
                    ).cardIndices[0]
                }, 1.5)
        }
    }

    protected override onDestroy(): void {
        // cat.event.dispatchEvent(GameEventConstant.HAND_CARD_STATE, true)
        store.game.stealSelectedCardIndex = undefined
    }
}
