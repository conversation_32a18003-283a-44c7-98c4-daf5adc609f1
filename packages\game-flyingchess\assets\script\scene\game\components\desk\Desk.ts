import {
    _decorator,
    Button,
    Camera,
    error,
    instantiate,
    isValid,
    Label,
    Node,
    Prefab,
    Sprite,
    SpriteFrame,
    tween,
    v3,
    Vec3,
} from 'cc'
import { BattleMode } from 'sgc'
import {
    GameEventConstant,
    AudioEffectConstant,
    GlobalEventConstant,
} from '@/core/business/constant'
import {
    watchUser,
    audioEffect,
    buttonLock,
} from '@/core/business/hooks/Decorator'
import store from '@/core/business/store'
import {
    GameModelSinglePosMap,
    Single,
    GameModelTeamPosMap,
    Team,
    CardChineseName,
} from '@/core/business/types/IGame'
import { SocketEvent } from '@/core/business/ws'
import { cat } from '@/core/manager'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { Card, CardState } from '@/pb-generate/server/pirate/v1/card_pb'

import {
    PostCardResponse,
    DrawCardRequestSchema,
    DrawCardResponseSchema,
    PostCardRequestSchema,
    PostCardResponseSchema,
} from '@/pb-generate/server/pirate/v1/handler_pb'
import { State as DeskState } from '@/pb-generate/server/pirate/v1/state_pb'

// import {
//     GameBroadcast,
//     DeskState,
//     RefreshCardRequestSchema,
//     RefreshCardResponseSchema,
//     CardState,
//     Draw,
// } from '@/pb-generate/server/pirate/v1/message_pb'

import { CardItem } from '../card/CardItem'
import { LightCardItem } from '../card/LightCardItem'
import { PlayerItem } from '../player/PlayerItem'
import { HandCard } from './HandCard'
import { reflect } from '@bufbuild/protobuf/reflect'
import { create, fromBinary } from '@bufbuild/protobuf'
import { UIDraw } from '../../ui/action/active/draw/UIDraw'
import { Barrel } from '../barrel/Barrel'
import { GameInfoBroadcast } from '@/pb-generate/server/pirate/v1/game_pb'
import {
    PlayerDrawnCardMessage,
    PlayerInfoMessage,
    PlayerPostedCardBroadcast,
} from '@/pb-generate/server/pirate/v1/player_pb'
import CardAnimationManager from './CardAnimationManager'
import { BasePool } from '@/core/manager/gui/base/BasePool'
import { sleep } from '@/core/business/util/TimeUtils'

const { ccclass, property, requireComponent } = _decorator

type DeskProps = {}

type DeskData = {}

@ccclass('Desk')
@requireComponent(CardAnimationManager)
export class Desk extends BaseComponent<DeskProps, DeskData> {
    @property({ type: Node, tooltip: '游戏区域节点' })
    card_move_tween: Node = null!

    @property({ type: Node, tooltip: '抽牌区' })
    draw_zone: Node = null!

    @property({ type: Node, tooltip: '抽牌后牌先飞到展示区' })
    draw_display_zone: Node = null!

    @property({ type: Node, tooltip: '玩家节点' })
    players_node: Node = null!

    @property({ type: Label, tooltip: '牌堆剩余张数' })
    remaining_deck_cards: Label = null!

    @property({ type: Label, tooltip: '炸弹(危险牙齿)数量' })
    boom_count: Label = null!

    @property({ type: Node, tooltip: '出牌区' })
    play_zone: Node = null!

    // @property({ type: sp.Skeleton, tooltip: '轮转顺序' })
    // player_direction: sp.Skeleton = null!

    @property({ type: HandCard, tooltip: '手牌组件' })
    hand_card: HandCard = null!

    @property({ type: Prefab, tooltip: '手牌预制体' })
    hand_card_item_prefab: Prefab = null!

    @property({ type: Prefab, tooltip: '玩家预制体' })
    players_prefab: Prefab = null!

    @property({ type: Node, tooltip: '操作按钮组' })
    action_node: Node = null!

    @property({ type: Node, tooltip: '抽牌按钮' })
    btn_action_draw: Node = null!

    @property({ type: [SpriteFrame], tooltip: '抽牌按钮精灵图集' })
    btn_action_draw_spriteFrames: SpriteFrame[] = []

    @property({ type: Node, tooltip: '出牌按钮' })
    btn_action_play: Node = null!

    @property({ type: Node, tooltip: '轮到你了节点' })
    your_trun_node: Node = null!

    @property({ type: Barrel, tooltip: '木桶节点' })
    barrel: Barrel = null!

    @property({ type: Camera, tooltip: '3D摄像机节点' })
    camera3D: Camera = null!

    @property({ type: Prefab, tooltip: '按钮UI预制体' })
    ui_draw_prefab: Prefab = null!

    @property({ type: [SpriteFrame], tooltip: '轮到你了图集' })
    your_trun_spriteFrame: SpriteFrame[] = []

    private cardAnimationManager: CardAnimationManager

    /**获取牌堆位置 */
    private drawZonePos: Vec3 = v3()
    /**获取发牌区位置 */
    private playZonePos: Vec3 = v3()
    /**获取手牌位置 */
    private handZonePos: Vec3 = v3()

    // 发牌进行中
    private isDealCardLoading: boolean = false

    private ui_draw: Node | null = null

    override data: DeskData = {
        showActionButton: false,
    }

    public cardPool: BasePool | null = null
    protected override initUI(): void {
        this.cardAnimationManager = this.getComponent(CardAnimationManager)!
        this.draw_zone.on(
            Node.EventType.TOUCH_END,
            this.onActionDrawHandler,
            this
        )
        this.btn_action_draw.on(
            Button.EventType.CLICK,
            this.onActionDrawHandler,
            this
        )
        this.btn_action_play.on(
            Button.EventType.CLICK,
            this.onActionplayCardManual,
            this
        )

        // this.your_trun_node.getComponent(Sprite)!.spriteFrame =
        //     this.your_trun_spriteFrame[store.user.isAudience ? 1 : 0]
    }

    /**初始化牌桌 */
    initDesk() {
        this.reset()
        this.cardPool = new BasePool(this.hand_card_item_prefab)
    }

    override onLoad(): void {}

    override start(): void {
        // 出牌区复位
        this.play_zone.removeAllChildren()
    }

    protected override onEventListener(): void {
        cat.event
            .on<SocketEvent>(
                'MT_GAME_INFO_BROADCAST',
                this.onBroadcastSatateSocketHandler,
                this
            )
            .on<SocketEvent>(
                'MT_STATE_CHANGED_BROADCAST',
                this.onBroadcastSatateSocketHandler,
                this
            )
            .on<SocketEvent>(
                'MT_PLAYER_INFO_MESSAGE',
                this.onPlayerInfoBroadcastHandler,
                this
            )
            // .on<SocketRoute>('RefreshCard', this.onWatchRefreshCard, this)

            .on(
                GlobalEventConstant.EVENT_SHOW,
                this.onHandleAppForeground,
                this
            )
            .on(
                GameEventConstant.AFTER_UPDATE_HANDCARD_LAYOUT,
                this.checkAndInitHandCard,
                this
            )
    }

    protected override onAutoObserver(): void {
        this.addAutorun([
            () => {
                // 更新剩余卡牌数量
                this.remaining_deck_cards.string = `${
                    store.game.roomData?.deckInfo?.deckRemainingCardCount || 0
                }`
            },
            () => {
                // 更新危险牙齿数量
                this.boom_count.string = `${
                    store.game.roomData?.deckInfo
                        ?.deckRemainingDeathCardCount || 0
                }`
            },
            () => {
                // 更新轮转方向
                // this.player_direction.node.scale = Vec3.ONE.clone().multiply3f(
                //     store.game.roomData.isForward ? -1 : 1,
                //     1,
                //     1
                // )
            },
            () => {
                this.btn_action_draw.active = this.btn_action_play.active =
                    store.game.showActionButton
            },
            () => {
                this.btn_action_play.getComponent(Button)!.interactable =
                    !!store.game.playerCards.length
                this.btn_action_play.getComponent(Sprite)!.grayscale =
                    !store.game.playerCards.length
            },
            () => {
                if (
                    [
                        DeskState.PLAYER_DRAW_OR_POST,
                        DeskState.PLAYER_HOSTING,
                    ].includes(store.game.roomData?.stateInfo?.state!) &&
                    store.game.roomData.roundInfo?.currentPlayerCursor ===
                        store.user.userIndex
                ) {
                    // 显示出牌
                    if (
                        store.game.lastRoomData.roundInfo
                            ?.currentPlayerCursor !== store.user.userIndex &&
                        !store.game.userTurn
                    ) {
                        store.game.userTurn = true
                        // cat.audio.playEffect(AudioEffectConstant.YOUR_TURN)
                        this.your_trun_node.active = true
                        this.scheduleOnce(() => {
                            this.your_trun_node.active = false
                        }, 2.0)
                    }
                } else {
                    store.game.userTurn = false
                    // 隐藏
                }
            },
            () => {
                this.btn_action_draw.getComponent(Sprite)!.spriteFrame =
                    this.btn_action_draw_spriteFrames[
                        store.game.getPlayerDrawCount - 1
                    ]
            },
            // () => {
            //     if (
            //         store.game.lastRoomData?.stateInfo?.state ===
            //             DeskState.UNSPECIFIED &&
            //         store.game.roomData?.stateInfo?.state ===
            //             DeskState.GAME_START
            //     ) {
            //         window.ccLog('DESK_STATE_GAME_START')
            //         cat.gui.hideLoading()
            //         this.onPhaseInit()
            //     }
            // },
        ])

        this.addReaction(
            () => store.game.playerInfo?.players,
            (players) => {
                players.forEach((player) => {
                    const find = this.players_node.children.find(
                        (item) =>
                            item.getComponent(PlayerItem)?.props.player
                                .index === player.index
                    )
                    if (find) {
                        find.getComponent(PlayerItem)?.setUpdateProps({
                            player,
                        })
                    }
                })
            }
        )
            .addReaction(
                () =>
                    store.game.lastRoomData?.stateInfo?.state ===
                        DeskState.UNSPECIFIED &&
                    store.game.roomData?.stateInfo?.state ===
                        DeskState.GAME_START &&
                    (store.game.playerCards.length > 0 ||
                        store.user.isAudience),
                (isGameStart) => {
                    if (isGameStart) {
                        window.ccLog('DESK_STATE_GAME_START')
                        cat.gui.hideLoading()
                        this.onPhaseInit()
                    }
                }
            )
            .addReaction(
                // 超时状态自动打开摸牌界面
                () =>
                    store.game.roomData.stateInfo?.state ===
                        DeskState.PLAYER_HOSTING &&
                    store.game.roomData.roundInfo?.currentPlayerCursor ===
                        store.game.playerInfo?.playerIndex,
                (is_open) => {
                    if (is_open) {
                        this.openDrawUI()
                    }
                },
                {
                    fireImmediately: true,
                }
            )
    }

    private onPlayerInfoBroadcastHandler(data: PlayerInfoMessage) {
        store.game.playerInfo = data
        // let find = data.players.find((item) => {
        //     console.log(item.id, store.game.getUserPlayer?.id)
        //     return item.id === store.game.getUserPlayer?.id
        // })

        // if (store.user.isAudience) {
        //     //观战取第一个玩家的牙齿状态 再处理(过滤坏牙好牙 当未使用处理)
        //     find = data.players[0]
        //     find.cardStates.forEach((item, index, arr) => {
        //         if ([CardState.BAD, CardState.GOOD].includes(item)) {
        //             arr[index] = CardState.UNUSED
        //         }
        //     })
        // }
        // if (find?.cardStates) {
        //     // 更新当前玩家的信息(手牌)
        //     store.game.player_teeth = find.cardStates
        //     this.barrel.getComponent(Barrel)?.setOptions({
        //         props: {
        //             card_state: find.cardStates,
        //         },
        //     })
        // }
    }
    private onBroadcastSatateSocketHandler(data: GameInfoBroadcast) {
        // 新消息的时间戳小于当前时间戳则丢弃
        if (
            data.stateInfo?.serverTimestamp &&
            store.game.roomData?.stateInfo?.serverTimestamp &&
            data.stateInfo?.serverTimestamp <
                store.game.roomData?.stateInfo?.serverTimestamp
        ) {
            window.ccLog('onBroadcastSatateSocketHandler 丢弃旧消息')
            return
        }

        store.game.roomData = data
        // 更新socket服务器时间
        if (data.stateInfo?.serverTimestamp) {
            const diff = Number(data.stateInfo?.serverTimestamp) - Date.now()
            store.global.ws_diff_server_timer = diff
            window.ccLog('ws时间差(ms)', diff)
        }
    }

    init() {
        this.initDesk()
    }

    /**初始化-阶段 */
    private onPhaseInit() {
        this.init()
        // this.barrel.initTooth(store.game.roomData.deckInfo?.deckCardStates)
        window.ccLog('初始化-阶段')
        // this.cardAnimationManager.dealCard(store.game.playerCards)
        cat.tracking.game.roomEnter()
    }

    /**初始化玩家位置信息 */
    initPlayers() {
        const { game, user } = store
        store.game.playersPos.clear()
        const { mode } = user.auth
        const { userIndex, userTeamId } = user
        window.ccLog('初始化玩家位置信息', userIndex, userTeamId)
        const { battle } = game
        // 按照索引从小到大 顺时针牌桌
        const copy = [...game.basePlayers]
        copy.sort((a, b) => a.relBattleOffset - b.relBattleOffset)
        if (!copy.length) return error('玩家列表不存在')
        if (mode == BattleMode.PVP_COOPERATION) {
            //team
            // 玩家排序 第一个始终是同队玩家
            const isInTeam = copy.find((item) => item.teamId == userTeamId)

            while (isInTeam && copy[0].teamId != userTeamId) {
                // 添加到末尾
                const nonce = copy.shift()
                nonce && copy.push(nonce)
            }
        } else if (mode == BattleMode.PVP_SOLO) {
            //solo
            // 查询自己是否在玩家列表
            const isInPalyer = copy.find(
                (item) => item.relBattleOffset === userIndex
            )
            // 玩家排序 第一个始终是玩家自己
            while (isInPalyer && copy[0].relBattleOffset != userIndex) {
                // 添加到末尾
                const nonce = copy.shift()
                nonce && copy.push(nonce)
            }
        } else {
            return error(`错误模式:${mode}`)
        }

        const playersPos =
            mode == BattleMode.PVP_SOLO
                ? GameModelSinglePosMap[battle.teams as Single]
                : mode == BattleMode.PVP_COOPERATION
                ? GameModelTeamPosMap[battle.teams as Team]
                : []

        copy.forEach((item, index) => {
            store.game.playersPos.set(item.relBattleOffset, playersPos[index])
        })

        window.ccLog('位置信息是否为空', playersPos)

        this.players_node.destroyAllChildren()

        store.game.playerInfo.players.forEach((item, index) => {
            const player_node = instantiate(this.players_prefab)
            const { x, y } = store.game.playersPos.get(item.index)!
            window.ccLog('重新实例玩家', index, item)

            player_node
                .getComponent(PlayerItem)!
                .setPosition(v3(x, y, 0))
                .addToParent(this.players_node, { props: { player: item } })
        })
    }

    onHandleAppForeground() {
        this.checkAndInitHandCard(true)
    }

    @buttonLock(0.1)
    checkAndInitHandCard(reLayout: boolean = false) {
        window.ccLog('检查手牌是否需要同步')
        if (this.cardAnimationManager.isDealCardLoading) {
            return
        }
        const cards = store.game.playerCards
        if (
            this.hand_card &&
            this.hand_card.getCardList().join(',') !== cards.join(',')
        ) {
            window.ccLog(
                '手牌状态不一致！！！！！！！！！',
                this.hand_card.getCardList().join(','),
                cards.join(',')
            )
            return this.initHandCard(cards, false)
        } else if (reLayout) {
            // 刷新手牌布局
            cat.event.dispatchEvent(GameEventConstant.UPDATE_HANDCARD_LAYOUT)
        }
    }

    /**同步手牌 */
    initHandCard(syncData: Card[], is_tween: boolean = false) {
        // 对比手牌和同步数据
        // 清除手牌
        window.ccLog('----------同步手牌')
        this.cardAnimationManager.dealCard(syncData, is_tween)
    }

    //#region 事件

    /**抽牌(按牙) */
    // @watchUser()
    // @audioEffect()
    @buttonLock(0.5)
    private onActionDrawHandler() {
        if (store.game.showActionButton) {
            this.openDrawUI()
        }
    }

    private openDrawUI() {
        window.ccLog('------------打开抽牌UI', !isValid(this.ui_draw))
        if (!isValid(this.ui_draw)) {
            // 打开按牙面板
            this.ui_draw = instantiate(this.ui_draw_prefab)
            cat.gui.openUI<UIDraw>(this.ui_draw, {
                props: {
                    barrel: this.barrel,
                    camera3D: this.camera3D,
                },
            })
        }
    }

    /**出牌(点击手牌) */
    @watchUser()
    @audioEffect()
    @buttonLock(0.5)
    private onActionplayCardManual() {
        // 获取选中的牌
        const selected_node = this.hand_card.getCardSelected()

        if (selected_node) {
            const selected = selected_node.getComponent(LightCardItem)!
            const index = selected_node.getSiblingIndex()
            window.ccLog('获取选中的牌', selected.props.card)
            cat.ws
                .Request(
                    'PostCard',
                    reflect(
                        PostCardRequestSchema,
                        create(PostCardRequestSchema, {
                            cards: [selected.props.card],
                            handCardIndices: [index],
                        })
                    ),
                    PostCardResponseSchema
                )
                .then((res) => {
                    store.game.selectedCardIndex = undefined

                    this.handleByCard(selected.props.card, res)
                })
        } else {
            cat.gui.showToast({ title: '请选择出牌' })
        }
    }

    private handleByCard(card: Card, res: PostCardResponse) {
        // 更新手牌
        // this.oncePlayAnimation(selected_node)
        cat.event.dispatchEvent(
            GameEventConstant.STOP_PLAYER_Timer,
            store.user.userIndex
        )
        switch (card) {
            case Card.PEEK:
                cat.event.dispatchEvent(
                    GameEventConstant.SCOUNT_CARD_RESPONSE,
                    res
                )
                break

            default:
                break
        }
    }

    /**添加卡牌到出牌出 */
    addPlayCard(card: Card) {
        const card_node = this.cardPool!.get()!
        const cardItem = card_node.getComponent(CardItem)!
        cardItem.addToParent(this.play_zone, { props: { card } })
    }

    /**复位 */
    reset() {
        this.cardAnimationManager.clearCardMoveTween()
        // 出牌区复位
        this.cardAnimationManager.clearPlayZone()
    }

    onShowCardInfoHandler() {
        this
    }
}
