import { _decorator, Node, sp, isValid } from 'cc'
import { cat } from '@/core/manager'
import { SocketEvent } from '@/core/business/ws'
import { AudioEffectConstant } from '@/core/business/constant'
import {
    Card,
    CardActiveDefenseBroadcast,
} from '@/pb-generate/server/pirate/v1/card_pb'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { CommonAction } from './common/CommonAction'

const { ccclass, property } = _decorator

/**
 * <防御 卡牌处理逻辑>
 */
@ccclass('DefenceCardAction')
export class DefenceCardAction extends BaseComponent {
    protected override onEventListener(): void {
        cat.event.on<SocketEvent>(
            'MT_CARD_ACTIVE_DEFENSE_BROADCAST',
            this.onGamePostHandler,
            this
        )
    }

    /**出牌广播 */
    private onGamePostHandler(data: CardActiveDefenseBroadcast) {
        cat.audio.playEffect(AudioEffectConstant.DEFENSE)
        window.ccLog('防御')
        this.getComponent(CommonAction)?.showCardEffect(Card.DEFENSE)
    }
}
