import {
    _decorator,
    Component,
    error,
    instantiate,
    isValid,
    log,
    Node,
    Prefab,
    sp,
    UITransform,
    Vec3,
} from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { Desk } from '../../../desk/Desk'
import CardAnimationManager from '../../../desk/CardAnimationManager'

const { ccclass, property } = _decorator

@ccclass('BaseAction')
export class BaseAction<T extends Object = {}> extends BaseComponent<T> {
    protected desk: Desk
    protected cardAnimationManager: CardAnimationManager
    override initUI(): void {
        this.desk = this.getComponent(Desk)!
        this.cardAnimationManager = this.getComponent(CardAnimationManager)!
    }
}
