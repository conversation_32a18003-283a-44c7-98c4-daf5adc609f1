import { _decorator, Node, sp, isValid, Prefab, instantiate } from 'cc'
import { cat } from '@/core/manager'
import { SocketEvent } from '@/core/business/ws'
import { AudioEffectConstant } from '@/core/business/constant'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import store from '@/core/business/store'
import { State } from '@/pb-generate/server/pirate/v1/state_pb'
import {
    Card,
    CardAfterPeekMessage,
} from '@/pb-generate/server/pirate/v1/card_pb'
import { ActivePeek } from '../../../ui/action/active/peek/ActivePeek'
import { CommonAction } from './common/CommonAction'

const { ccclass, property } = _decorator

/**
 * <透视 卡牌处理逻辑>
 */
@ccclass('PeekCardAction')
export class PeekCardAction extends BaseComponent {
    @property({ type: Prefab, tooltip: 'UI主动侦查-预制体' })
    action_peek_ui_prefab: Prefab

    protected override onEventListener(): void {
        cat.event.on<SocketEvent>(
            'MT_CARD_AFTER_PEEK_MESSAGE',
            this.onGamePostHandler,
            this
        )
    }

    protected override onAutoObserver(): void {
        this.addReaction(
            () =>
                store.game.roomData.stateInfo?.state === State.CARD_PEEK &&
                store.game.roomData.roundInfo?.currentPlayerCursor ===
                    store.game.playerInfo?.playerIndex,
            (is_open) => {
                if (is_open) {
                    const node = instantiate(this.action_peek_ui_prefab)
                    cat.gui.openUI<ActivePeek>(node)
                }
            }
        )

        this.addReaction(
            () =>
                store.game.roomData.stateInfo?.state === State.CARD_PEEK &&
                !(
                    store.game.roomData.roundInfo?.currentPlayerCursor ===
                    store.game.playerInfo?.playerIndex
                ),
            (showPeekEffect) => {
                if (showPeekEffect) {
                    this.getComponent(CommonAction)?.showCardEffect(Card.PEEK)
                }
            }
        )
    }

    /**出牌 */
    private onGamePostHandler(data: CardAfterPeekMessage) {
        if (!store.game.isUserOrTeammate(data.fromPlayerIndex)) {
            cat.audio.playEffect(AudioEffectConstant.INSPECT)
        }
    }
}
