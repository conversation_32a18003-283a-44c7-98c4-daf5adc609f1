import {
    _decorator,
    Component,
    instantiate,
    log,
    Node,
    Prefab,
    UITransform,
    Vec3,
    sp,
    isValid,
    tween,
    find,
} from 'cc'

import { PlayerItem } from '../../player/PlayerItem'
import { Steal } from '@/script/scene/game/ui/action/active/steal/steal'
import { SelectPlayerAction } from './common/SelectPlayerAction'
import { LightCardItem } from '../LightCardItem'
import { CommonAction } from './common/CommonAction'
import { cat } from '@/core/manager'
import { AudioEffectConstant } from '@/core/business/constant'
import { audioEffect } from '@/core/business/hooks/Decorator'
import store from '@/core/business/store'
import { SocketEvent } from '@/core/business/ws'
import { PlayerAfterSelectTargetBroadcast } from '@/pb-generate/server/pirate/v1/player_pb'
import {
    Card,
    CardAfterStealMessage,
} from '@/pb-generate/server/pirate/v1/card_pb'
import { State as DeskState } from '@/pb-generate/server/pirate/v1/state_pb'

const { ccclass, property } = _decorator

/**
 * <索取卡牌处理逻辑>
 *  1.A发起索取->选择目标B->发送服务器
    2.所有人接收服务器推送(选择目标状态)A向B发起索取
    3.B响应被动索取->选择卡牌->发送服务器
    4.所有人接收服务器推送(Game.GiveMeYourCardBroadcast) A 从 B 手牌中抽取一张牌
 * 
*/

@ccclass('StealCardAction')
export class StealCardAction extends SelectPlayerAction {
    @property({ type: Prefab, tooltip: '交互-被动-索取UI预制体' })
    passive_request_prefab: Prefab = null!

    @property({ type: Node, tooltip: '索取小动画-容器-节点' })
    give_me_your_card_node: Node = null!

    @property({ type: sp.Skeleton, tooltip: '索取小动画-动画-节点' })
    give_me_your_card_skeleton: sp.Skeleton = null!

    private play_zone: Node

    protected override start(): void {
        const gameRoot = find('Game UI')
        const desk = gameRoot?.getComponentInChildren('Desk')!.node
        this.play_zone = desk?.getChildByName('play_zone')!
    }

    protected override onEventListener(): void {
        super.onEventListener()
        cat.event
            .on<SocketEvent>(
                'MT_PLAYER_AFTER_SELECT_TARGET_BROADCAST',
                this.onSelectTargetBroadcast,
                this
            )
            .on<SocketEvent>(
                'MT_CARD_AFTER_STEAL_MESSAGE',
                this.onCardAfterStealMessage,
                this
            )
    }

    protected override onAutoObserver(): void {
        super.onAutoObserver
        this.addReaction(
            () => store.game.roomData.stateInfo?.state,
            (state) => {
                if (state === DeskState.PLAYER_SELECT_TARGET) {
                    if (Card.STEAL === store.game.getLastCardOnPostZone) {
                        window.ccLog(
                            '索取-指定阶段',
                            state === DeskState.PLAYER_SELECT_TARGET
                        )
                        // cat.audio.playEffect(AudioEffectConstant.REQUEST)
                        this.onStateGameSelectTarget(store.game.roomData)
                    }
                }
            }
        )

        this.addAutorun([
            () => {
                if (
                    store.game.roomData.stateInfo?.state ===
                    DeskState.CARD_STEAL
                ) {
                    if (Card.STEAL === store.game.getLastCardOnPostZone) {
                        cat.audio.playEffect(AudioEffectConstant.REQUEST)
                        window.ccLog(
                            `${store.game.roomData.roundInfo?.currentPlayerCursor}向${store.game.selectTarget.targetPlayerIndex}发起索取`
                        )
                        if (
                            [
                                store.game.roomData.roundInfo
                                    ?.currentPlayerCursor,
                                store.game.selectTarget.targetPlayerIndex,
                            ].includes(store.user.userIndex)
                        ) {
                            const node = instantiate(
                                this.passive_request_prefab
                            )
                            const requestPlayer = store.game.getPlayerByIndex(
                                store.game.roomData.roundInfo
                                    ?.currentPlayerCursor!
                            )!
                            const responsePlayer = store.game.getPlayerByIndex(
                                store.game.selectTarget.targetPlayerIndex
                            )!

                            const isRequestPlayer =
                                store.game.roomData.roundInfo
                                    ?.currentPlayerCursor ===
                                store.user.userIndex
                            cat.gui.openUI<Steal>(node, {
                                props: {
                                    desk: this.desk,
                                    requestPlayer,
                                    responsePlayer,
                                    isRequestPlayer,
                                },
                                isMotion: false,
                            })
                        } else {
                        }
                    }
                }
            },
        ])
    }

    /**所有人-接收索取请求 */
    override onSelectTargetBroadcast(data: PlayerAfterSelectTargetBroadcast) {
        super.onSelectTargetBroadcast(data)
        if (store.game.getLastCardOnPostZone === Card.STEAL) {
            const to = store.game.getPlayerComponentByIndex(
                this.desk.players_node,
                data.targetPlayerIndex
            )!

            this.playGiveMeYourCardAnimation({ to })
        }
    }

    // 在被索取人头像上播放索取动画
    playGiveMeYourCardAnimation({ to }: { to: PlayerItem }) {
        const cb = () => {
            if (isValid(this.give_me_your_card_skeleton.node)) {
                this.give_me_your_card_skeleton.node.active = false
            }
        }
        this.give_me_your_card_skeleton.setCompleteListener(cb)
        this.give_me_your_card_skeleton.node.active = true
        this.give_me_your_card_skeleton.setAnimation(0, 'idle', false)

        const fromPosition = this.play_zone
            .parent!.getComponent(UITransform)!
            .convertToNodeSpaceAR(this.play_zone.worldPosition)

        const toPosition = to.node
            .parent!.getComponent(UITransform)!
            .convertToNodeSpaceAR(to.node.worldPosition)

        this.give_me_your_card_node.setPosition(fromPosition)
        this.give_me_your_card_node.setScale(0.4, 0.4)

        tween(this.give_me_your_card_node)
            // .delay(0.3)
            .to(0.3, { position: toPosition })
            .delay(0.8)
            .call(() => {
                // this.give_me_your_card_node.destroy()
            })
            .start()
    }

    /**所有人-接收索取响应 */
    // @audioEffect(AudioEffectConstant.REQUEST_FINISHED)
    private async onCardAfterStealMessage(data: CardAfterStealMessage) {
        window.ccLog(
            `${data.targetPlayerIndex}向${data.fromPlayerIndex}响应索取-card:${data.targetPlayerCards}`
        )

        // 响应节点
        const from = store.game.getPlayerComponentByIndex(
            this.desk.players_node,
            data.targetPlayerIndex
        )!.node
        // 请求节点
        const to = store.game.getPlayerComponentByIndex(
            this.desk.players_node,
            data.fromPlayerIndex
        )!.node
        //索取的卡牌
        const card = data.targetPlayerCards[0]
        // 动画时间
        const duration = 0.25

        if (store.game.isUserOrWatchTeammate(data.fromPlayerIndex)) {
            this.cardAnimationManager.stealTween({
                from,
                card,
            })
        } else if (store.game.isUserOrWatchTeammate(data.targetPlayerIndex)) {
            // 用户是响应者
            this.cardAnimationManager.stealFromTween({
                to,
                targetPlayerHandCardIndices: data.targetPlayerHandCardIndices,
            })
        } else {
            // 其他人 显示 索取者->被索取者 飞牌动画
            this.cardAnimationManager.otherStealTween({
                from,
                to,
            })
        }
    }
}
