[{"__type__": "cc.Prefab", "_name": "player_item", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "player_item", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 282}], "_active": true, "_components": [{"__id__": 291}, {"__id__": 293}], "_prefab": {"__id__": 295}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "border", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}], "_active": true, "_components": [{"__id__": 279}, {"__id__": 195}], "_prefab": {"__id__": 281}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -10.5125, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "player", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 4}, {"__id__": 68}, {"__id__": 136}, {"__id__": 204}, {"__id__": 218}, {"__id__": 248}], "_active": true, "_components": [{"__id__": 276}, {"__id__": 193}], "_prefab": {"__id__": 278}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 5}, {"__id__": 15}, {"__id__": 61}], "_active": true, "_components": [{"__id__": 65}], "_prefab": {"__id__": 67}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "avatar_border", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 4}, "_children": [{"__id__": 6}], "_active": true, "_components": [{"__id__": 10}, {"__id__": 12}], "_prefab": {"__id__": 14}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "avatar", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 5}, "_children": [], "_active": true, "_components": [{"__id__": 7}], "_prefab": {"__id__": 9}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 6}, "_enabled": true, "__prefab": {"__id__": 8}, "_contentSize": {"__type__": "cc.Size", "width": 135, "height": 135}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "78wq6tF4pDiZkbEV5Vlu80"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b0WR9nno9O2YuW8Bc6yNQI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 5}, "_enabled": true, "__prefab": {"__id__": 11}, "_contentSize": {"__type__": "cc.Size", "width": 144, "height": 144}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aa++mdGfFPXJvV7TA8Qf6e"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 5}, "_enabled": false, "__prefab": {"__id__": 13}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "289b65c8-7e62-4eb9-987b-687eb754004b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "41PS2o8KlOEqrdkqPA3b59"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b23mfc831LQ4SB0dAfxMqr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "state", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 4}, "_children": [{"__id__": 16}, {"__id__": 48}], "_active": true, "_components": [{"__id__": 56}, {"__id__": 58}], "_prefab": {"__id__": 60}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "tag", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 15}, "_children": [{"__id__": 17}, {"__id__": 23}, {"__id__": 29}, {"__id__": 35}], "_active": false, "_components": [{"__id__": 41}, {"__id__": 43}, {"__id__": 45}], "_prefab": {"__id__": 47}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "auto", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 16}, "_children": [], "_active": false, "_components": [{"__id__": 18}, {"__id__": 20}], "_prefab": {"__id__": 22}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 17}, "_enabled": true, "__prefab": {"__id__": 19}, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 47}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "abH4sdAfxNgLSFzayzikHn"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 17}, "_enabled": true, "__prefab": {"__id__": 21}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c0572daf-4f9d-4ee7-a26b-1c389e39ce6e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71uqzep91BGIj/XYkWFuIF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "00qufRE4pGiKgYqkYgHu4w", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "out", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 16}, "_children": [], "_active": false, "_components": [{"__id__": 24}, {"__id__": 26}], "_prefab": {"__id__": 28}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 23}, "_enabled": true, "__prefab": {"__id__": 25}, "_contentSize": {"__type__": "cc.Size", "width": 76, "height": 47}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a5e3j2cGpCoYfK/cLy4g4F"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 23}, "_enabled": true, "__prefab": {"__id__": 27}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2b8ec72f-18bc-4f19-a733-a21b81c32032@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "09nRrqFfJGxo/uJEpWejEt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c6YtMJfd5Ip7MpnfockQ/F", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "leave", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 16}, "_children": [], "_active": false, "_components": [{"__id__": 30}, {"__id__": 32}], "_prefab": {"__id__": 34}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": {"__id__": 31}, "_contentSize": {"__type__": "cc.Size", "width": 77, "height": 47}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a4enQTRTRKP5urYSqiO+oZ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": {"__id__": 33}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "98d5f1a9-1f85-4546-99c9-3ab58913144e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7dlhENYaNN27MUUz2S5uZI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "94+d+q171Jn4Rmgok84C1Z", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "watch", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 16}, "_children": [], "_active": false, "_components": [{"__id__": 36}, {"__id__": 38}], "_prefab": {"__id__": 40}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 35}, "_enabled": true, "__prefab": {"__id__": 37}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2c5pOYlo1IBrEEhWjz+J+W"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 35}, "_enabled": true, "__prefab": {"__id__": 39}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ca3d261b-f6b5-4ee3-ad12-c74482a98dd0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dcFfEuD6dOZ4vUnOETGCPd"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b3JO6/wlNGe56w7kkj83Mh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 16}, "_enabled": true, "__prefab": {"__id__": 42}, "_contentSize": {"__type__": "cc.Size", "width": 121, "height": 121}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90Q0k5RXdIKrIPIAiAmymP"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 16}, "_enabled": true, "__prefab": {"__id__": 44}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "071beb37-167f-4eac-826d-009da8c7fe14@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "17fpuY9WVE07KRVETyRcCa"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 16}, "_enabled": true, "__prefab": {"__id__": 46}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 121, "_originalHeight": 121, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "26ocEsyMFH8KJfkT0o5IiE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c5kJCPzr5NEZySWllF5EY/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "team_tag", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 15}, "_children": [], "_active": true, "_components": [{"__id__": 49}, {"__id__": 51}, {"__id__": 53}], "_prefab": {"__id__": 55}, "_lpos": {"__type__": "cc.Vec3", "x": 45.301, "y": 66.831, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 48}, "_enabled": true, "__prefab": {"__id__": 50}, "_contentSize": {"__type__": "cc.Size", "width": 81, "height": 41}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "421tRTsjRM8bkmVDqpOP1v"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 48}, "_enabled": true, "__prefab": {"__id__": 52}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3ek9MOcy9HPIgOjy5NN1oM"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 48}, "_enabled": true, "__prefab": {"__id__": 54}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": -20.801000000000002, "_top": -22.331000000000003, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 1, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "81X4s0VgxBFa4/72wFvBMo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "99E9sQ+UVLnIzb8EoKWWVs", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 15}, "_enabled": true, "__prefab": {"__id__": 57}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5dWnYLvv9CnIJeiZyXKBi4"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 15}, "_enabled": true, "__prefab": {"__id__": 59}, "_alignFlags": 45, "_target": null, "_left": 7, "_right": 7, "_top": 7, "_bottom": 7, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 121, "_originalHeight": 121, "_alignMode": 1, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3rO+xO8RIpbPyqK4S/ObU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "47WTizsDhA6oEMrAa/uKxT", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "dress_container", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 4}, "_children": [], "_active": true, "_components": [{"__id__": 62}], "_prefab": {"__id__": 64}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 61}, "_enabled": true, "__prefab": {"__id__": 63}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "36UY9wGQBNo6Y+JOczge9p"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "10wMW8jRBDWaWTHbkNTW1O", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 66}, "_contentSize": {"__type__": "cc.Size", "width": 144, "height": 144}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d6rflivcdLTJ8Nb6LTHpnf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "65+1jC/GRPu7SdDRsqD0Lm", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "tween", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 69}, {"__id__": 101}, {"__id__": 117}], "_active": true, "_components": [{"__id__": 133}], "_prefab": {"__id__": 135}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 10.512500000000045, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "strengthen", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 68}, "_children": [{"__id__": 70}, {"__id__": 78}], "_active": true, "_components": [{"__id__": 96}, {"__id__": 98}], "_prefab": {"__id__": 100}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "spine_slot", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 69}, "_children": [{"__id__": 71}], "_active": true, "_components": [{"__id__": 75}], "_prefab": {"__id__": 77}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 70}, "_children": [], "_active": true, "_components": [{"__id__": 72}], "_prefab": {"__id__": 74}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 71}, "_enabled": true, "__prefab": {"__id__": 73}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "36hzlG1idG4r95VcECwKkX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "624itQ2wtDmYJ6KMJiTFc8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 70}, "_enabled": true, "__prefab": {"__id__": 76}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "87CB8reIhI1IMNqwti5a8g"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0aipoLb+tD2YMOlrIt3aI7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 69}, "_children": [{"__id__": 79}, {"__id__": 85}], "_active": true, "_components": [{"__id__": 91}, {"__id__": 93}], "_prefab": {"__id__": 95}, "_lpos": {"__type__": "cc.Vec3", "x": 67.534, "y": 42.715, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.9, "y": 0.9, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "knife", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 78}, "_children": [], "_active": true, "_components": [{"__id__": 80}, {"__id__": 82}], "_prefab": {"__id__": 84}, "_lpos": {"__type__": "cc.Vec3", "x": -32.565324520338756, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 79}, "_enabled": true, "__prefab": {"__id__": 81}, "_contentSize": {"__type__": "cc.Size", "width": 51, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "88/QOOQkJDNYdbop9u0h24"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 79}, "_enabled": true, "__prefab": {"__id__": 83}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bf598f07-b4ff-450c-927e-b7aac78d0d9d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "baCvhRIWdCNr2un/UdNjPN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1fSaBY+AdN7LX2lkY836rT", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "count", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 78}, "_children": [], "_active": true, "_components": [{"__id__": 86}, {"__id__": 88}], "_prefab": {"__id__": 90}, "_lpos": {"__type__": "cc.Vec3", "x": 25.434675479661244, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 85}, "_enabled": true, "__prefab": {"__id__": 87}, "_contentSize": {"__type__": "cc.Size", "width": 65, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0ezJPKpgdCyIGb57eibjYS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 85}, "_enabled": true, "__prefab": {"__id__": 89}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d85494ca-332c-4311-8ea2-5f225fdc41d4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "248b6wO1hFc4hGM7wZWIEa"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4001z6pgNLI6xkXwnqUgLL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 78}, "_enabled": true, "__prefab": {"__id__": 92}, "_contentSize": {"__type__": "cc.Size", "width": 116, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5005631424167134, "y": 0.49754295197944187}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "32k+trqYVK+5QhgX5kTKIm"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 78}, "_enabled": true, "__prefab": {"__id__": 94}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4fAlESB21NPLpCXFspx2FS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "deWfoYD71PO43I/QtBxT2I", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 69}, "_enabled": true, "__prefab": {"__id__": 97}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e37HBKXAFEq5QXVHl0iSet"}, {"__type__": "cce51g2BRVL9rVUq9/akggZ", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 69}, "_enabled": true, "__prefab": {"__id__": 99}, "spine_slot": {"__id__": 70}, "count_strengthen_node": {"__id__": 78}, "strengthen_count": {"__id__": 88}, "strengthen_count_spriteFrame": [{"__uuid__": "d85494ca-332c-4311-8ea2-5f225fdc41d4@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "e9989a30-c70f-4d2a-aa13-cfad6ed1835d@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "5a7f7310-cb6e-45a9-a7bf-801894517db4@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "37d7f20d-701a-4e41-b7f8-8bc9588bed95@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "3f1a751a-acec-4f1f-b4d7-a5de4933a877@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "dde3399c-e9cf-4bc9-86b5-c31cc754c900@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "f196c43e-2a36-42e7-8564-8e79b57df332@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "0a211c33-f0ac-47a4-8d9e-838687bc4969@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "51cc0e85-f610-461e-9ed2-fd35abfcb4f4@f9941", "__expectedType__": "cc.SpriteFrame"}], "spine_strengthen": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9fgE96MLJKMZIS+LmRg6sW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "66bxCy26JJpoE6Ey+FSsLk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "freeze", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 68}, "_children": [{"__id__": 102}], "_active": true, "_components": [{"__id__": 112}, {"__id__": 114}], "_prefab": {"__id__": 116}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -1.483, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "spine_slot", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 101}, "_children": [{"__id__": 103}], "_active": true, "_components": [{"__id__": 109}], "_prefab": {"__id__": 111}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 102}, "_children": [], "_active": true, "_components": [{"__id__": 104}, {"__id__": 106}], "_prefab": {"__id__": 108}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.75, "y": 0.75, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 103}, "_enabled": true, "__prefab": {"__id__": 105}, "_contentSize": {"__type__": "cc.Size", "width": 337.3599853515625, "height": 367.1499938964844}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.519889752801817, "y": 0.4788778707532543}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "feUFBMD7pCFp0kBBaDjI/w"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 103}, "_enabled": true, "__prefab": {"__id__": 107}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "6bec4a70-52a6-4e69-9b51-508d555680e2", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "admission", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "15LoNJHrtBH54quj8ykiPL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d9KcuI8+RG1aPc6/4KBWqX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 102}, "_enabled": true, "__prefab": {"__id__": 110}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "63+JWCsHdEw6vab4I9azJ9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "97aSJhlDxGbo696JZWYLKl", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 101}, "_enabled": true, "__prefab": {"__id__": 113}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f2UJvvaWFBWK9fFTvh5dzS"}, {"__type__": "4d87eq6B5ZPTKiUDHW5b0mm", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 101}, "_enabled": true, "__prefab": {"__id__": 115}, "spine_slot": {"__id__": 102}, "spine_freeze": {"__id__": 106}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f5mJ8t7HdNQaU5Vqc3wscJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "26+Ltz6pRKzYMHqzKdMIi5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "defer", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 68}, "_children": [{"__id__": 118}], "_active": true, "_components": [{"__id__": 128}, {"__id__": 130}], "_prefab": {"__id__": 132}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 55.395, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "spine_slot", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 117}, "_children": [{"__id__": 119}], "_active": true, "_components": [{"__id__": 125}], "_prefab": {"__id__": 127}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 118}, "_children": [], "_active": true, "_components": [{"__id__": 120}, {"__id__": 122}], "_prefab": {"__id__": 124}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 119}, "_enabled": true, "__prefab": {"__id__": 121}, "_contentSize": {"__type__": "cc.Size", "width": 112.13999938964844, "height": 112.13999938964844}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.48992330056834704, "y": 0.4861779985241945}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bdv95M4ttKJ6dhye1XMMko"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 119}, "_enabled": true, "__prefab": {"__id__": 123}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "64a77cd8-8403-40e3-8611-fc038ab981f9", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "00cCDOWalLA7lUvFofFzmh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "51JZkL/LdMW5IY69Ko/jYe", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 118}, "_enabled": true, "__prefab": {"__id__": 126}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "53NJT2BrZJz60/zYm5kpRi"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d6U7ngrtlEyJxc/PVQFT6U", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 117}, "_enabled": true, "__prefab": {"__id__": 129}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6ePUW1ICtH/bql3lxWTY56"}, {"__type__": "6b093bU+SFCBaKZ9Vnx1Uee", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 117}, "_enabled": true, "__prefab": {"__id__": 131}, "spine_slot": {"__id__": 118}, "spine_defer": {"__id__": 122}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ef6rP2XXxHz6E9c1zGPCSg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "08M4Wa7BtFu6U2LfWuNMM9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 68}, "_enabled": true, "__prefab": {"__id__": 134}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "18mOqmqm1LeptGQVX2egUI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "50py+8qmxKSojMyULzZ48/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Countdown", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 137}], "_active": true, "_components": [{"__id__": 189}, {"__id__": 191}], "_prefab": {"__id__": 203}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "countdown", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 136}, "_children": [{"__id__": 138}], "_active": true, "_components": [{"__id__": 184}, {"__id__": 186}], "_prefab": {"__id__": 188}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.7071067811865475, "w": 0.7071067811865476}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 137}, "_prefab": {"__id__": 139}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 138}, "asset": {"__uuid__": "2f65931d-cde4-4384-b955-279c8927f3d3", "__expectedType__": "cc.Prefab"}, "fileId": "57lc+k+ixJpJCfphQW9Jwl", "instance": {"__id__": 140}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "a9/WeFsz5AXYYRhY5aTBUN", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 141}, {"__id__": 143}, {"__id__": 144}, {"__id__": 145}, {"__id__": 146}, {"__id__": 147}, {"__id__": 149}, {"__id__": 151}, {"__id__": 153}, {"__id__": 154}, {"__id__": 156}, {"__id__": 158}, {"__id__": 160}, {"__id__": 162}, {"__id__": 164}, {"__id__": 165}, {"__id__": 167}, {"__id__": 169}, {"__id__": 171}, {"__id__": 172}, {"__id__": 173}, {"__id__": 174}, {"__id__": 175}, {"__id__": 176}, {"__id__": 177}, {"__id__": 178}, {"__id__": 180}, {"__id__": 182}], "removedComponents": [{"__id__": 183}]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 142}, "propertyPath": ["_name"], "value": "time"}, {"__type__": "cc.TargetInfo", "localID": ["57lc+k+ixJpJCfphQW9Jwl"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 142}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -47.078, "y": -15.457, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 142}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": -0.7071067811865475, "w": 0.7071067811865476}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 142}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 142}, "propertyPath": ["_layer"], "value": 16384}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 148}, "propertyPath": ["_layer"], "value": 16384}, {"__type__": "cc.TargetInfo", "localID": ["e1mZeOYG1IXKGy3HAnfC01"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 150}, "propertyPath": ["_layer"], "value": 16384}, {"__type__": "cc.TargetInfo", "localID": ["5555PXCehPaZ4yc9gKf5QV"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 152}, "propertyPath": ["_layer"], "value": 16384}, {"__type__": "cc.TargetInfo", "localID": ["52EYBhBddOjY4dvY20phS1"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 142}, "propertyPath": ["_active"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 155}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 53, "height": 58}}, {"__type__": "cc.TargetInfo", "localID": ["57cU2RGuhBzYD7csHlWKFJ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 157}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "2ce09917-56a7-46f9-961d-c4ea8e7a8f2c@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["5adrA30eBJm7aasZnGDKBY"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 159}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "c8f66244-acbb-4266-aeb8-f9cc37e17b49@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["c5NAwKcWVCXKHaoaXrVq+P"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 161}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 111, "height": 32}}, {"__type__": "cc.TargetInfo", "localID": ["61GXN1+XxLUrl7pLiD+chz"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 163}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "854552d4-7471-4e21-9a94-97a6480ea0ef@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["c1CL8b7SxNGLRVP0QYMsqN"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 148}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -52.682, "y": 0.0019999999999988916, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 166}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 105, "height": 26}}, {"__type__": "cc.TargetInfo", "localID": ["b6F6NH8CxPV5TLx67n3k5A"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 168}, "propertyPath": ["_totalLength"], "value": 111}, {"__type__": "cc.TargetInfo", "localID": ["f1nYAMlWZPi4IiB04e1Bgw"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 170}, "propertyPath": ["_alignFlags"], "value": 41}, {"__type__": "cc.TargetInfo", "localID": ["44O0Y5KKpMjqNWUHkw2N0Z"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 170}, "propertyPath": ["_left"], "value": 2.817999999999998}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 170}, "propertyPath": ["_originalHeight"], "value": 32}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 170}, "propertyPath": ["_originalWidth"], "value": 111}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 170}, "propertyPath": ["_top"], "value": 2.998}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 170}, "propertyPath": ["_right"], "value": 3.1820000000000093}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 159}, "propertyPath": ["_type"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 150}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -65.172, "y": 3.524, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 179}, "propertyPath": ["_fontSize"], "value": 22}, {"__type__": "cc.TargetInfo", "localID": ["0fmqrVyUVJhZbt8rl9PcXF"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 181}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 27.0849609375, "height": 31.72}}, {"__type__": "cc.TargetInfo", "localID": ["edIuwAo0VHN6YfJkmprsH9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 179}, "propertyPath": ["_actualFontSize"], "value": 22}, {"__type__": "cc.TargetInfo", "localID": ["66xhH67ntA1av+sEAh+gzK"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 137}, "_enabled": true, "__prefab": {"__id__": 185}, "_contentSize": {"__type__": "cc.Size", "width": 121, "height": 121}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "42RrOtZJBKM5+/ortn/CBk"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 137}, "_enabled": false, "__prefab": {"__id__": 187}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "071beb37-167f-4eac-826d-009da8c7fe14@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 3, "_fillType": 2, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_fillStart": 0, "_fillRange": 1, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "67rN9RaX5CGLHofdQ+yeWu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "16eGKpHwtKsYalRwFMcY8s", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 136}, "_enabled": true, "__prefab": {"__id__": 190}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f37VTDzDZNqocRVmKeo7E+"}, {"__type__": "3cc786jkU1D3Y1FPYyBWneB", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 136}, "_enabled": true, "__prefab": {"__id__": 192}, "player_node": {"__id__": 193}, "countdown_node": {"__id__": 186}, "border_spriteFrame": [{"__uuid__": "289b65c8-7e62-4eb9-987b-687eb754004b@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "045c0b93-2373-4b5f-8635-5c03cf8090fe@f9941", "__expectedType__": "cc.SpriteFrame"}], "border": {"__id__": 195}, "time_progres": null, "time_count": {"__id__": 197}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4cZokuLGtKDrc1lc+W+uN0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 194}, "_customMaterial": {"__uuid__": "1f1dca05-8255-4d56-af1d-51e858e868ad", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "cd47002c-8b9f-45f5-a57a-aac1fc229379@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21CVHs5zBN/5ZM0ViVjV6A"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": false, "__prefab": {"__id__": 196}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "045c0b93-2373-4b5f-8635-5c03cf8090fe@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b68o26U5tA14HorhuYFHDt"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 198}, "_enabled": true, "__prefab": {"__id__": 202}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "0s", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "Source Han Sans CN", "_lineHeight": 0, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 13, "g": 104, "b": 97, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.Node", "_name": "count", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 138}, "_children": [], "_active": true, "_components": [{"__id__": 199}, {"__id__": 197}], "_prefab": {"__id__": 201}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 198}, "_enabled": true, "__prefab": {"__id__": 200}, "_contentSize": {"__type__": "cc.Size", "width": 27.0849609375, "height": 31.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "edIuwAo0VHN6YfJkmprsH9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "52EYBhBddOjY4dvY20phS1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.CompPrefabInfo", "fileId": "0fmqrVyUVJhZbt8rl9PcXF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9baDlB85FA5K3t0rClCXy+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "card_count", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 205}], "_active": true, "_components": [{"__id__": 211}, {"__id__": 213}, {"__id__": 215}], "_prefab": {"__id__": 217}, "_lpos": {"__type__": "cc.Vec3", "x": -57.122, "y": 42.286, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.7692307692307692, "y": 0.7692307692307692, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 204}, "_children": [], "_active": true, "_components": [{"__id__": 206}, {"__id__": 208}], "_prefab": {"__id__": 210}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 205}, "_enabled": true, "__prefab": {"__id__": 207}, "_contentSize": {"__type__": "cc.Size", "width": 19.734375, "height": 40.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0fjPN4s55NRoLFvzDc4XbK"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 205}, "_enabled": true, "__prefab": {"__id__": 209}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "Nowar Rounded TW", "_lineHeight": 0, "_overflow": 0, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 150, "g": 71, "b": 17, "a": 255}, "_outlineWidth": 3, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9akDT4MHpME5Sp5wEOp8rq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eccCzj72xHBbAaqhsqkW8q", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 204}, "_enabled": true, "__prefab": {"__id__": 212}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 53}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9Sk6OXrxA1YNS6L5WGeO2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 204}, "_enabled": true, "__prefab": {"__id__": 214}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bfc3a5c9-6864-49c6-baa0-f1baea8cbbb6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bdJ++BJV5N54IMi/2JZFHZ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 204}, "_enabled": true, "__prefab": {"__id__": 216}, "_alignFlags": 9, "_target": null, "_left": -10.50661538461539, "_right": 0, "_top": -0.6706153846153811, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 1, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "97X3pdvq5Bf6mwJ79OFkmB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2173ax+zJBj4uTD4jhM0HC", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "nick_name", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 219}, {"__id__": 225}], "_active": true, "_components": [{"__id__": 241}, {"__id__": 243}, {"__id__": 245}], "_prefab": {"__id__": 247}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -87.39, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 218}, "_children": [], "_active": true, "_components": [{"__id__": 220}, {"__id__": 222}], "_prefab": {"__id__": 224}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 3.01, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 219}, "_enabled": true, "__prefab": {"__id__": 221}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 28.98}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "450FGjAgBGCYFGWVlajYcL"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 219}, "_enabled": true, "__prefab": {"__id__": 223}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 23, "_fontSize": 23, "_fontFamily": "Source Han Sans CN", "_lineHeight": 0, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6aADI8H6JF1qlLsDqpa/jL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "75/lENjGRMvLZ0mOahI3gz", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "select", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 218}, "_children": [{"__id__": 226}, {"__id__": 232}], "_active": true, "_components": [{"__id__": 238}], "_prefab": {"__id__": 240}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 124.884, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "arrow", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 225}, "_children": [], "_active": false, "_components": [{"__id__": 227}, {"__id__": 229}], "_prefab": {"__id__": 231}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -107.932, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": -1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 226}, "_enabled": true, "__prefab": {"__id__": 228}, "_contentSize": {"__type__": "cc.Size", "width": 221.75999450683594, "height": 221.75999450683594}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5005411379399106, "y": 0.500450965485177}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "32WCn1OpVCC7vhKuQhJURf"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 226}, "_enabled": true, "__prefab": {"__id__": 230}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "61a8e14c-f8a1-4ff4-b682-335df09ec129", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "Selected1", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a9tThFGvlDbbo7GRP8B9Dy"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c4YH/CgXxOmaTIGhVDeWM0", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "selected", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 225}, "_children": [], "_active": false, "_components": [{"__id__": 233}, {"__id__": 235}], "_prefab": {"__id__": 237}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -35.161, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 232}, "_enabled": true, "__prefab": {"__id__": 234}, "_contentSize": {"__type__": "cc.Size", "width": 221.75999450683594, "height": 221.75999450683594}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5005411379399106, "y": 0.500450965485177}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "53tkRsCGtP0KHTYHTfza8e"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 232}, "_enabled": true, "__prefab": {"__id__": 236}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "61a8e14c-f8a1-4ff4-b682-335df09ec129", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "Selected2", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b0v4sz5phPaI2D9Frk8aJ3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "72FSZxaRpPJJFFbTfihvrI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 225}, "_enabled": true, "__prefab": {"__id__": 239}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 41}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ac+54PH1pN+4/RfnvD8l84"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f8tpMW9nFLD43mKWG0bus3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 218}, "_enabled": true, "__prefab": {"__id__": 242}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7bEsfL50RAsK5S+mrSj2sl"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 218}, "_enabled": false, "__prefab": {"__id__": 244}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5b149ae5-e2cc-4e82-a36f-92ac31eaa6c8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c1VmrTCpRH76whs6XjDESa"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 218}, "_enabled": true, "__prefab": {"__id__": 246}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": -42.89, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 1, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84p7rmseZJVKsHQNoGicCl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "48E2+uC+VOVKwGkfQ0mVKY", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "score", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 249}, {"__id__": 255}, {"__id__": 261}], "_active": true, "_components": [{"__id__": 267}, {"__id__": 269}, {"__id__": 271}, {"__id__": 273}], "_prefab": {"__id__": 275}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -125.154, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "icon", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 248}, "_children": [], "_active": true, "_components": [{"__id__": 250}, {"__id__": 252}], "_prefab": {"__id__": 254}, "_lpos": {"__type__": "cc.Vec3", "x": -32.565, "y": -0.184, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 249}, "_enabled": true, "__prefab": {"__id__": 251}, "_contentSize": {"__type__": "cc.Size", "width": 58, "height": 45}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "56FTLyUYZF07ZIo1VRRPX0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 249}, "_enabled": true, "__prefab": {"__id__": 253}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9a23c86e-ccfd-4cbe-b27f-a7ec12e46de7@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ecllhDTKxE36sa9QX9fgT5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d36cH5/QFKGa5906U6fWNA", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "score", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 248}, "_children": [], "_active": true, "_components": [{"__id__": 256}, {"__id__": 258}], "_prefab": {"__id__": 260}, "_lpos": {"__type__": "cc.Vec3", "x": 20.087, "y": 0.611, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 255}, "_enabled": true, "__prefab": {"__id__": 257}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 40.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e6j+IU9Q5GV6JbpTy+Hnqr"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 255}, "_enabled": true, "__prefab": {"__id__": 259}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "Source Han Sans CN", "_lineHeight": 0, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83LHfu5rNP3YRQT9Y4c7GT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4clovDr5tPB75ltNl/PWEt", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "change", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 248}, "_children": [], "_active": true, "_components": [{"__id__": 262}, {"__id__": 264}], "_prefab": {"__id__": 266}, "_lpos": {"__type__": "cc.Vec3", "x": 1.826, "y": 0.611, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": -1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 261}, "_enabled": true, "__prefab": {"__id__": 263}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 40.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0645qZXiJG3bc5Lr5CmGpa"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 261}, "_enabled": true, "__prefab": {"__id__": 265}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "Source Han Sans CN", "_lineHeight": 0, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "57UeJXmONHTLb63Ut2ggUS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "32LfO96qhLfKYc4gT1KTjp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 248}, "_enabled": true, "__prefab": {"__id__": 268}, "_contentSize": {"__type__": "cc.Size", "width": 103, "height": 43}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "16HUW1DcRON5BTty0F2Rx0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 248}, "_enabled": true, "__prefab": {"__id__": 270}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b3ff45c1-fc11-4ff7-a3fb-ea5220a997da@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7cg4NayZBHrpdgBq8o4rSO"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 248}, "_enabled": true, "__prefab": {"__id__": 272}, "_alignFlags": 18, "_target": null, "_left": 33.5, "_right": 33.5, "_top": 208.37, "_bottom": -81.37, "_horizontalCenter": 0, "_verticalCenter": -125.154, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 103, "_originalHeight": 43, "_alignMode": 1, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79w2r57QtHB7vbEphcD9AG"}, {"__type__": "1aa5dxBCu9Oxo1P5Z5vvCfB", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 248}, "_enabled": true, "__prefab": {"__id__": 274}, "score_icon": {"__id__": 249}, "score_count": {"__id__": 258}, "score_change": {"__id__": 264}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "35w3lHmjtGibUr/5FV1WCu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "14CZfoZsNLprdS0ZvVLEzd", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 277}, "_contentSize": {"__type__": "cc.Size", "width": 124, "height": 124}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8brTWM6ZLv7mVP1olb7yg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2aAEZlbWpJM7VVJfR3NRBR", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 280}, "_contentSize": {"__type__": "cc.Size", "width": 170, "height": 170}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83mcFHUdxCeZFvak1q90tN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "63fk6N3GRJVYwJrRvaSIUe", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 283}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 282}, "asset": {"__uuid__": "ddc75272-970f-46a2-8e06-4caa0be79613", "__expectedType__": "cc.Prefab"}, "fileId": "873KEkFjVHAIgSZMub22XD", "instance": {"__id__": 284}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "ccu8RW4oRD2oykLAOpIvk1", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 285}, {"__id__": 287}, {"__id__": 288}, {"__id__": 289}, {"__id__": 290}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 286}, "propertyPath": ["_name"], "value": "draw_mine"}, {"__type__": "cc.TargetInfo", "localID": ["873KEkFjVHAIgSZMub22XD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 286}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 286}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 286}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 286}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 292}, "_contentSize": {"__type__": "cc.Size", "width": 144, "height": 144}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "18Hrqxz1dKqI9m+9zo3y3D"}, {"__type__": "2c08afmBpNGJJIm4yIWKtUs", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 294}, "player_node": {"__id__": 193}, "nick_name_node": {"__id__": 222}, "avatar_node": {"__id__": 193}, "card_count_node": {"__id__": 208}, "select_arrow_node": {"__id__": 226}, "selected_node": {"__id__": 232}, "countdown": {"__id__": 191}, "tag_node": {"__id__": 16}, "auto_node": {"__id__": 17}, "out_node": {"__id__": 23}, "leave_node": {"__id__": 29}, "watch_node": {"__id__": 35}, "draw_mine_node": {"__id__": 282}, "score": {"__id__": 273}, "strengthen": {"__id__": 98}, "freeze": {"__id__": 114}, "defer": {"__id__": 130}, "dressPrefab": {"__uuid__": "9e8029d9-f9d1-41c5-9c34-6d312f1e810f", "__expectedType__": "cc.Prefab"}, "dress_container": {"__id__": 61}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "deG9lVFWVLBZzu0lbF2tS2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0aikPZyhpB54CvRuoSwMiF", "instance": null, "targetOverrides": [{"__id__": 296}, {"__id__": 298}, {"__id__": 300}], "nestedPrefabInstanceRoots": [{"__id__": 282}, {"__id__": 138}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 191}, "sourceInfo": null, "propertyPath": ["time_progres"], "target": {"__id__": 138}, "targetInfo": {"__id__": 297}}, {"__type__": "cc.TargetInfo", "localID": ["f1nYAMlWZPi4IiB04e1Bgw"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 191}, "sourceInfo": null, "propertyPath": ["time_count"], "target": {"__id__": 138}, "targetInfo": {"__id__": 299}}, {"__type__": "cc.TargetInfo", "localID": ["0fmqrVyUVJhZbt8rl9PcXF"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 138}, "sourceInfo": {"__id__": 301}, "propertyPath": ["_barSprite"], "target": {"__id__": 138}, "targetInfo": {"__id__": 302}}, {"__type__": "cc.TargetInfo", "localID": ["f1nYAMlWZPi4IiB04e1Bgw"]}, {"__type__": "cc.TargetInfo", "localID": ["c5NAwKcWVCXKHaoaXrVq+P"]}]