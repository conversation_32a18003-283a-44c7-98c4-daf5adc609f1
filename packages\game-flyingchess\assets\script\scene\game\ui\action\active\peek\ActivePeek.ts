import {
    _decorator,
    Button,
    Component,
    Input,
    instantiate,
    isValid,
    Label,
    log,
    Node,
    Prefab,
    sp,
    Sprite,
    Sprite<PERSON>rame,
    Texture2D,
    tween,
    v3,
    Vec3,
} from 'cc'
import { AudioSourceCommonActionUI } from '../common/AudioSourceCommonActionUI'
// import {
//     Card,
//     CardState,
//     DeskState,
//     selectDiagnoseBroadcast,
//     SelectDiagnoseRequestSchema,
//     SelectDiagnoseResponseSchema,
// } from '@/pb-generate/server/pirate/v1/message_pb'
import { audioEffect, watchUser } from '@/core/business/hooks/Decorator'
import { cat } from '@/core/manager'
import { reflect } from '@bufbuild/protobuf/reflect'
import { create } from '@bufbuild/protobuf'
import store from '@/core/business/store'
import { SocketEvent } from '@/core/business/ws'
import { LightCardItem } from '@/script/scene/game/components/card/LightCardItem'
import { PeekCardItem } from '@/script/scene/game/components/card/PeekCardItem'
import { UIOpacity } from 'cc'
import {
    AudioEffectConstant,
    GameEventConstant,
} from '@/core/business/constant'
import { TimeCount } from '@/script/scene/game/components/time-count/TimeCount'
import GetTimeDifferenceFromServer from '@/core/business/hooks/GetTimeDifferenceFromServer'
import { Barrel, ZoomState } from '@/script/scene/game/components/barrel/Barrel'
import { JSBridgeClient } from '@/core/business/jsbridge/JSBridge'
import { KnifeHole } from '@/script/scene/game/components/barrel/KnifeHole'
import {
    PeekCardRequestSchema,
    PeekCardResponse,
    PeekCardResponseSchema,
} from '@/pb-generate/server/pirate/v1/handler_pb'
import { State } from '@/pb-generate/server/pirate/v1/state_pb'
import {
    Card,
    CardAfterPeekMessage,
    CardState,
} from '@/pb-generate/server/pirate/v1/card_pb'
import { sleep } from '@/core/business/util/TimeUtils'
import { BasePool } from '@/core/manager/gui/base/BasePool'

const { ccclass, property } = _decorator

type ActivePeekProps = {}
enum Stage {
    TO_BE_SCOUT = 0,
    SCOUTED,
}

type ActivePeekData = {
    stage: Stage
}

@ccclass('ActivePeek')
export class ActivePeek extends AudioSourceCommonActionUI<
    ActivePeekProps,
    ActivePeekData
> {
    @property({ type: Node, tooltip: '标题' })
    title: Node

    @property({ type: [SpriteFrame], tooltip: '标题节点精灵图' })
    title_spriteFrame: SpriteFrame[] = []

    @property({ type: Node, tooltip: '木桶模型节点' })
    barrel: Node

    @property({ type: Node, tooltip: '确定按钮' })
    btn_ok: Node

    @property({ type: Label, tooltip: '确定倒计时' })
    ok_time_out: Label

    @property({ type: Node, tooltip: '透视按钮' })
    btn_scout: Node

    @property({ type: Label, tooltip: '选择透视的缝隙数量' })
    scout_count: Label

    @property({ type: TimeCount, tooltip: '倒计时时间' })
    time_count: TimeCount

    @property({ type: Node, tooltip: '主节点' })
    main: Node

    @property({ type: Prefab, tooltip: '透视结果预制体' })
    peek_card_item_prefab: Prefab = null!

    public cardPool: BasePool | null = null

    override props: ActivePeekProps = {}

    override data: ActivePeekData = {
        stage: Stage.TO_BE_SCOUT,
    }

    protected override initUI(): void {
        this.cardPool = new BasePool(this.peek_card_item_prefab)
    }

    override onLoad(): void {
        JSBridgeClient.disableTouch(true)
        this.btn_scout.on(Button.EventType.CLICK, this.onScoutHandler, this)
        this.btn_ok.on(Button.EventType.CLICK, this.onOkHandler, this)
    }

    protected override onEventListener(): void {
        cat.event
            .on<SocketEvent>(
                'MT_CARD_AFTER_PEEK_MESSAGE',
                this.onPeekedAction,
                this
            )
            .on(
                GameEventConstant.DRAW_SCOUT_TIME_OUT,
                ({ time }: { time: number }) => {
                    this.ok_time_out.string = `${time}s`
                },
                this
            )
    }

    override start(): void {
        store.game.selected_tooth = []
        store.game.layerStatus.peek = true
        store.game.click_tooth_type = 2
        store.game.is_allow_click_tooth = true

        const limit =
            store.game.getUnusedTeeth.length < 2
                ? store.game.getUnusedTeeth.length
                : 2
        this.title.getComponent(Sprite)!.spriteFrame =
            this.title_spriteFrame[limit - 1]

        this.playAudio()
    }

    protected override onAutoObserver(): void {
        this.addAutorun([
            () => {
                !(this.time_count.node.active =
                    this.btn_scout.active =
                    this.title.active =
                        this.data.stage === Stage.TO_BE_SCOUT)
            },
            () => {
                const limit =
                    store.game.getUnusedTeeth.length < 2
                        ? store.game.getUnusedTeeth.length
                        : 2
                this.scout_count.string = `（${store.game.selected_tooth.length}/${limit}）`
            },
        ])
        // this.addReaction(
        //     () => store.game.roomData.stateInfo?.state,
        //     async (state) => {
        //         if (state === State.CARD_AFTER_PEEK) {
        //             window.ccLog(
        //                 '关闭 透视',
        //                 state,
        //                 state === State.CARD_AFTER_PEEK
        //             )
        //             await sleep(2000)
        //             // 关闭界面
        //             this.close()
        //         }
        //     }
        // )
    }

    protected override onDestroy(): void {
        JSBridgeClient.disableTouch(false)
        store.game.click_tooth_type = 0
    }

    private close() {
        if (isValid(this, true)) {
            store.game.layerStatus.peek = false
            cat.gui.closeUI(this)
        }
    }

    /**诊断 */
    @watchUser()
    @audioEffect()
    private onScoutHandler() {
        // this.onCloseCommonUIHandler()
        const limit =
            store.game.getUnusedTeeth.length < 2
                ? store.game.getUnusedTeeth.length
                : 2
        if (store.game.selected_tooth.length === limit) {
            const selected_tooth_index: number[] = []
            store.game.selected_tooth.forEach((item) => {
                selected_tooth_index.push(
                    item.getComponent(KnifeHole)?.props.index!
                )
            })
            cat.ws.Request(
                'PeekCard',
                reflect(
                    PeekCardRequestSchema,
                    create(PeekCardRequestSchema, {
                        deckCardIndices: selected_tooth_index,
                    })
                ),
                PeekCardResponseSchema
            )
        } else {
            cat.gui.showToast({ title: '请选择缝隙' })
        }
    }

    /*透视结果 */
    private async onPeekedAction(res: CardAfterPeekMessage) {
        window.ccLog('ActivePeek-->onPeekedAction, res:', res)
        store.game.is_allow_click_tooth = false
        this.data.stage = Stage.SCOUTED

        const len = res.peekedCardStates.length
        const pos = v3(0, 554, 0)

        setTimeout(() => {
            res.peekedCardStates.forEach((item, index) => {
                cat.event.dispatchEvent(GameEventConstant.TAG_TOOTH_STATE, {
                    index: res.deckCardIndices[index],
                    state: CardState.UNUSED,
                })
            })
        }, 50)

        // 依次处理每个牙齿
        for (let index = 0; index < res.peekedCardStates.length; index++) {
            const item = res.peekedCardStates[index]
            const toothIndex = res.deckCardIndices[index]

            await this.rotateBarrelToTooth(toothIndex)
            // 让牙齿颜色闪烁几次
            this.flashToothColor(toothIndex, item)

            // 从对象池获取卡牌节点
            const cardNode = this.cardPool?.get()
            if (!cardNode) continue

            // 卡牌从屏幕中心位置从小到大飞到屏幕上方位置
            await this.animateCard(cardNode, pos, item)

            // 悬停0.5秒
            await sleep(500)

            // 缩小渐隐消失
            await this.fadeOutCard(cardNode)

            // 回收卡牌节点
            this.cardPool?.put(cardNode)
        }
        await sleep(300)
        // 关闭界面
        this.close()
    }

    /**
     * 旋转海盗桶到指定牙齿位置
     */
    private rotateBarrelToTooth(toothIndex: number): Promise<void> {
        return new Promise<void>((resolve) => {
            // 通过事件触发桶的旋转
            cat.event.dispatchEvent(GameEventConstant.MOCK_SHARK_ROTATE, {
                toothIndex: toothIndex,
            })

            // 等待一段时间后认为旋转完成
            this.scheduleOnce(() => {
                resolve()
            }, 0.2) // 稍微比动画时间长一点，确保动画完成
        })
    }

    /**
     * 让牙齿颜色闪烁几次
     */
    private flashToothColor(
        toothIndex: number,
        finalState: CardState
    ): Promise<void> {
        return new Promise<void>((resolve) => {
            // 闪烁次数
            const flashCount = 3
            let currentFlash = 0

            // 闪烁间隔时间(毫秒)
            const flashInterval = 150

            // 闪烁定时器
            const flashTimer = setInterval(() => {
                // 奇数次设置为白色(UNUSED)，偶数次设置为最终颜色
                const state =
                    currentFlash % 2 === 0 ? finalState : CardState.UNUSED

                // 发送事件改变牙齿颜色
                cat.event.dispatchEvent(GameEventConstant.TAG_TOOTH_STATE, {
                    index: toothIndex,
                    state,
                })

                currentFlash++

                // 闪烁完成后，设置为最终颜色并清除定时器
                if (currentFlash >= flashCount * 2) {
                    clearInterval(flashTimer)

                    // 确保最终状态是正确的
                    cat.event.dispatchEvent(GameEventConstant.TAG_TOOTH_STATE, {
                        index: toothIndex,
                        state: finalState,
                    })

                    resolve()
                }
            }, flashInterval)
        })
    }

    /**
     * 卡牌从小到大飞行动画
     */
    private animateCard(
        cardNode: Node,
        targetPos: Vec3,
        state: CardState
    ): Promise<void> {
        return new Promise<void>((resolve) => {
            // 设置卡牌初始位置和缩放
            cardNode.setPosition(v3(0, 0, 0)) // 屏幕中心
            cardNode.setScale(v3(0.2, 0.2, 1)) // 初始缩小状态

            // 添加到父节点
            cardNode.setParent(this.main)

            // 设置卡牌状态
            const cardState =
                state === CardState.BAD ? CardState.BAD : CardState.GOOD
            const peekCardItem = cardNode.getComponent(PeekCardItem)
            peekCardItem?.setUpdateProps({ cardState })

            // 播放音效
            cat.audio.playEffect(AudioEffectConstant.INSPECT)

            // 从小到大飞到目标位置
            tween(cardNode)
                .to(0.5, {
                    position: targetPos,
                    scale: v3(1, 1, 1),
                })
                .call(() => {
                    resolve()
                })
                .start()
        })
    }

    /**
     * 卡牌缩小渐隐消失动画
     */
    private fadeOutCard(cardNode: Node): Promise<void> {
        return new Promise<void>((resolve) => {
            // 获取或添加UIOpacity组件
            let uiOpacity = cardNode.getComponent(UIOpacity)
            if (!uiOpacity) {
                uiOpacity = cardNode.addComponent(UIOpacity)
            }

            // 确保初始透明度为255
            uiOpacity.opacity = 255

            // 同时执行缩放和透明度动画
            tween(cardNode)
                .to(0.3, {
                    scale: v3(0.2, 0.2, 1),
                })
                .start()

            tween(uiOpacity)
                .to(0.3, {
                    opacity: 0,
                })
                .call(() => {
                    // 重置透明度，以便下次使用
                    uiOpacity.opacity = 255
                    resolve()
                })
                .start()
        })
    }

    /**确定 */
    private onOkHandler() {
        this.close()
    }
}
