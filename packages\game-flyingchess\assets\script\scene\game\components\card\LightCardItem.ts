import {
    _decorator,
    Component,
    Input,
    Label,
    log,
    math,
    Node,
    Sprite,
    Sprite<PERSON>rame,
    Tween,
    tween,
    v3,
    Vec3,
} from 'cc'
import { CardItem } from './CardItem'
import { Card } from '@/pb-generate/server/pirate/v1/card_pb'
import { cat } from '@/core/manager'
import { GameEventConstant } from '@/core/business/constant'
import store from '@/core/business/store'
import {
    audioEffect,
    buttonLock,
    watchUser,
} from '@/core/business/hooks/Decorator'

const { ccclass, property } = _decorator

export type CardItemProps = {
    card: Card
    is_steal_card?: boolean
}

export type CardItemData = {
    is_gray: boolean
    is_selected: boolean
}

@ccclass('LightCardItem')
export class LightCardItem extends CardItem<
    CardItemProps,
    Partial<CardItemData>
> {
    @property({ type: [SpriteFrame], tooltip: '卡牌精灵图集' })
    card_spriteFrames: SpriteFrame[] = []

    origin_offest_y: number = 0

    override props: CardItemProps = {
        card: Card.UNSPECIFIED,
        is_steal_card: false,
    }

    override data: CardItemData = {
        is_gray: false,
        is_selected: false,
    }

    protected override onLoad(): void {
        this.origin_offest_y = this.card.getPosition().y
    }

    protected override onAutoObserver(): void {
        this.addAutorun([
            () => {
                this.card.getComponent(Sprite)!.spriteFrame =
                    this.card_spriteFrames[this.props.card]
            },
            () => {
                this.card.getComponent(Sprite)!.color = this.data.is_gray
                    ? math.Color.GRAY
                    : math.Color.WHITE
            },
        ])

        this.addReaction(
            () => this.data.is_selected,
            (is_selected) => {
                this.selectTween(is_selected)
            }
        )
    }

    override start() {
        this.node.on(Node.EventType.TOUCH_END, this.onCardHandler, this)
    }

    @watchUser()
    @audioEffect()
    @buttonLock(0.2)
    private onCardHandler() {
        if (!this.isHandCard) return
        const selectedCardIndex = this.currentIndex
        window.ccLog(`选中的牌: ${selectedCardIndex}`, this.props.is_steal_card)
        cat.event.dispatchEvent(
            this.props.is_steal_card
                ? GameEventConstant.STEAL_SELECT_HAND_CARD
                : GameEventConstant.SELECT_HAND_CARD,
            selectedCardIndex
        )
    }

    /**
     * 选择动画
     * @param is_selected 是否选择牌
     * @returns
     */
    selectTween(is_selected: boolean) {
        return new Promise<void>((resolve) => {
            tween(this.card)
                .to(this.selected_duration, {
                    position: v3(
                        0,
                        is_selected
                            ? this.origin_offest_y + 32
                            : this.origin_offest_y,
                        0
                    ),
                })
                .call(() => {
                    Tween.stopAllByTarget(this.card)
                    resolve()
                })
                .start()
        })
    }

    /**设置为手牌 */
    setHandCard(isHandCard: boolean = true) {
        this.isHandCard = isHandCard
        if (!isHandCard && this.data.is_gray) this.data.is_gray = false
        return this
    }

    /**设置父节点 */
    setParent(parent: Node, keepWorldTransform: boolean = false) {
        this.node.setParent(parent, keepWorldTransform)
        return this
    }
}
