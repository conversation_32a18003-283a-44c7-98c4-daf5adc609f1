import {
    _decorator,
    Component,
    instantiate,
    log,
    Node,
    Prefab,
    UITransform,
    v3,
} from 'cc'
import { SelectPlayerAction } from './common/SelectPlayerAction'

import { cat } from '@/core/manager'
import { SocketEvent } from '@/core/business/ws'

import {
    Card,
    CardAfterFreezeBroadcast,
    CardAfterSwapMessage,
} from '@/pb-generate/server/pirate/v1/card_pb'
import { State as DeskState } from '@/pb-generate/server/pirate/v1/state_pb'
import {
    AudioEffectConstant,
    GameEventConstant,
} from '@/core/business/constant'
import store from '@/core/business/store'
import { audioEffect } from '@/core/business/hooks/Decorator'
import { CommonAction } from './common/CommonAction'

const { ccclass, property } = _decorator

/**
 * <冻结卡牌处理逻辑>
 */
@ccclass('FreezeCardAction')
export class FreezeCardAction extends SelectPlayerAction {
    protected override onEventListener(): void {
        super.onEventListener()
        cat.event.on<SocketEvent>(
            'MT_CARD_AFTER_FREEZE_BROADCAST',
            this.onCardAfterFreezeBroadcastHandler,
            this
        )
    }

    protected override onAutoObserver(): void {
        super.onAutoObserver
        this.addReaction(
            () => store.game.roomData.stateInfo?.state,
            (state) => {
                if (state === DeskState.PLAYER_SELECT_TARGET) {
                    if (Card.FREEZE === store.game.getLastCardOnPostZone) {
                        window.ccLog(
                            '冻结选人阶段',
                            state === DeskState.PLAYER_SELECT_TARGET
                        )

                        this.onStateGameSelectTarget(store.game.roomData)
                    }
                }
            }
        )
    }

    onCardAfterFreezeBroadcastHandler(data: CardAfterFreezeBroadcast) {
        this.scheduleOnce(() => {
            this.onCardAfterFreezeBroadcast(data)
        }, 0.2)
    }

    private onCardAfterFreezeBroadcast(data: CardAfterFreezeBroadcast) {
        window.ccLog(
            `${data.fromPlayerIndex}向${data.targetPlayerIndex}发起指向`
        )
        cat.audio.playEffect(AudioEffectConstant.FROZEN)

        // 响应节点
        const from = store.game.getPlayerComponentByIndex(
            this.desk.players_node,
            data.targetPlayerIndex
        )!.node
        // 请求节点
        const to = store.game.getPlayerComponentByIndex(
            this.desk.players_node,
            data.fromPlayerIndex
        )!.node

        this.getComponent(CommonAction)?.showCardEffect(Card.FREEZE)
    }
}
