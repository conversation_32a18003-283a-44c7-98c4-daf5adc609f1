import { GameEventConstant } from '@/core/business/constant'
import { cat } from '@/core/manager'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { CardState } from '@/pb-generate/server/pirate/v1/card_pb'
import {
    _decorator,
    Component,
    Material,
    MeshRenderer,
    Node,
    tween,
    Vec3,
} from 'cc'
const { ccclass, property } = _decorator

type KnifeHoleProps = {
    state?: CardState
    index?: number
    colorMaterials: Material[]
    angle?: number
}

type KnifeHoleData = {
    is_selected: boolean
}

enum Color {
    WHITE,
    RED,
    GREEN,
    BLUE,
}

@ccclass('KnifeHole')
export class KnifeHole extends BaseComponent<KnifeHoleProps, KnifeHoleData> {
    @property({ type: Node, tooltip: '小刀插槽' })
    hole: Node = null!

    @property({ type: Node, tooltip: '小刀' })
    knife: Node = null!

    //hole 为空，则可以插入小刀
    private is_empty: boolean = true

    override props: KnifeHoleProps = {
        state: CardState.UNSPECIFIED,
        index: -1,
        colorMaterials: [],
    }
    protected override onLoad(): void {}

    override onDestroy(): void {}

    protected override start(): void {}

    protected override onEventListener(): void {
        cat.event.on(
            GameEventConstant.TAG_TOOTH_STATE,
            ({ index, state }: { index: number; state: CardState }) => {
                if (this.props.index === index) {
                    window.ccLog('TAG_TOOTH_STATE--------', { index, state })
                    this.props.state = state
                }
            },
            this
        )
    }

    protected override onAutoObserver(): void {
        this.addAutorun([
            () => {
                this.updateUI()
            },
        ])
    }

    selected() {
        this.setMaterial(Color.BLUE)
    }

    unselected() {
        this.updateUI()
    }

    private updateUI() {
        let color = Color.WHITE
        switch (this.props.state) {
            case CardState.USED:
                this.knifeInsertHole()
                break
            case CardState.UNUSED:
                break
            case CardState.BAD:
                color = Color.RED
                break
            case CardState.GOOD:
                color = Color.GREEN
                break
            default:
                break
        }
        this.setMaterial(color)
    }

    private setMaterial(color: Color) {
        const materials = this.hole.getComponent(MeshRenderer)
        const colorMaterials = this.props.colorMaterials![color]
        colorMaterials &&
            materials?.setMaterialInstance(this.props.colorMaterials![color], 0)
    }
    //插入小刀
    knifeInsertHole() {
        if (!this.is_empty) return
        this.is_empty = false
        this.knife.active = true

        const initPosition = this.knife.position.clone()
        const startPosition = new Vec3(
            initPosition.x,
            initPosition.y,
            initPosition.z + 0.05
        )
        this.knife.setPosition(startPosition)

        tween(this.knife)
            .to(0.3, { position: initPosition })
            .call(() => {})
            .start()
    }
}
