import { _decorator, Node, sp, isValid } from 'cc'
import { cat } from '@/core/manager'
import { SocketEvent } from '@/core/business/ws'
import { AudioEffectConstant } from '@/core/business/constant'
import {
    Card,
    CardActiveDefenseBroadcast,
    CardStrengthenInvalidBroadcast,
} from '@/pb-generate/server/pirate/v1/card_pb'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { CommonAction } from './common/CommonAction'

const { ccclass, property } = _decorator

/**
 * <虚弱 卡牌处理逻辑>
 */
@ccclass('WeakenCardAction')
export class WeakenCardAction extends BaseComponent {
    protected override onEventListener(): void {
        cat.event.on<SocketEvent>(
            'MT_CARD_STRENGTHEN_INVALID_BROADCAST',
            this.onWeakenHandler,
            this
        )
    }

    private onWeakenHandler(data: CardStrengthenInvalidBroadcast) {
        cat.audio.playEffect(AudioEffectConstant.DEBILITATE)
        this.getComponent(CommonAction)?.showCardEffect(Card.WEAKEN)
    }
}
