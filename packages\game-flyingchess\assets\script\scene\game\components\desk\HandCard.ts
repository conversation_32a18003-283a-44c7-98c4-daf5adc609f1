import { _decorator, Node, Sprite, Sprite<PERSON>rame, v3 } from 'cc'
import { BaseHandCard } from './BaseHandCard'
import { cat } from '@/core/manager'
import { GameEventConstant } from '@/core/business/constant'
import store from '@/core/business/store'

const { ccclass, property } = _decorator

export enum PlayerHandCardState {
    NONE,
    NO_CARD,
    Team,
    Enemy,
}

type HandCardProps = {}

type HandCardData = {
    /**手牌状态 */
    state: PlayerHandCardState
}

/**手牌列表容器 */
@ccclass('HandCard')
export class HandCard extends BaseHandCard<HandCardProps, HandCardData> {
    @property({ type: Node, tooltip: '玩家牌桌状态节点' })
    player_desk_state_node: Node = null!

    @property({ type: [SpriteFrame], tooltip: '玩家牌桌状态精灵图' })
    player_desk_state_spriteFrame: SpriteFrame[] = []

    override data: HandCardData = {
        state: PlayerHandCardState.NONE,
    }

    protected override onEventListener(): void {
        super.onEventListener()
        cat.event
            // .on<SocketEvent>('EVENT_RANKING', this.onGameRankingHandler, this)
            // .on<SocketEvent>('EVENT_MEOVER', this.onGameTeamOutHandler, this)
            // .on(
            //     GameEventConstant.UPDATE_WATCH_HANDCARD,
            //     this.onUpdateWatchHandCardHandler,
            //     this
            // )
            .on(GameEventConstant.HAND_CARD_STATE, this.onHandCardState, this)
    }

    private onHandCardState(is_hide: boolean) {
        this.list.active = is_hide
        window.ccLog('显示手牌')
    }

    protected override onAutoObserver(): void {
        super.onAutoObserver()
        if (!store.user.isAudience) {
            this.addAutorun([
                () => {
                    const user = store.game.getPlayerByIndex(
                        store.user.userIndex
                    )
                    this.data.state = store.game.playerCards.length
                        ? PlayerHandCardState.NONE
                        : PlayerHandCardState.NO_CARD
                },
            ])
            this.addReaction(
                () => this.data.state,
                (state) => {
                    window.ccLog('设置牌桌状态', state)
                    if (state === PlayerHandCardState.NONE) {
                        //正常
                        this.player_desk_state_node.active = false
                    } else {
                        this.player_desk_state_node.getComponent(
                            Sprite
                        )!.spriteFrame = this.player_desk_state_spriteFrame[0]
                        this.player_desk_state_node.active =
                            !store.user.isAudience
                    }
                },
                {
                    fireImmediately: true,
                }
            )
        }
    }
}
