import {
    _decorator,
    CCBoolean,
    Component,
    instantiate,
    Layout,
    Node,
    Prefab,
    UITransform,
} from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import store from '@/core/business/store'
import { LightCardItem } from '../card/LightCardItem'
import { Card } from '@/pb-generate/server/pirate/v1/card_pb'
import { cat } from '@/core/manager'
import {
    AudioEffectConstant,
    GameEventConstant,
} from '@/core/business/constant'
import { reflect } from '@bufbuild/protobuf/reflect'
import { ClientDataBroadcastSchema } from '@/pb-generate/server/pirate/v1/game_pb'
import { create } from '@bufbuild/protobuf'

const { ccclass, property } = _decorator

@ccclass('BaseHandCard')
export class BaseHandCard<
    T extends object = {},
    K extends object = {}
> extends BaseComponent<T, K> {
    @property({ type: Node, tooltip: '卡牌列表' })
    list: Node = null!

    @property({ type: Prefab, tooltip: '卡牌预制体' })
    card_prefab: Prefab = null!

    @property({ type: CCBoolean, tooltip: '通用操作UI' })
    is_steal_card: boolean = false

    /**游戏容器尺寸 */
    private limit: number = 0
    /**间隔 */
    private gap: number = -80

    override onLoad(): void {
        this.limit = this.getComponent(UITransform)!.width - 200
    }

    protected override onEventListener(): void {
        cat.event
            .on(
                GameEventConstant.UPDATE_HANDCARD_LAYOUT,
                this.onUpdateHandCardLayout,
                this
            )
            .on(
                GameEventConstant.SELECT_HAND_CARD,
                this.onSelectedCardHandler,
                this
            )
            .on(
                GameEventConstant.STEAL_SELECT_HAND_CARD,
                this.onStealSelectedCardHandler,
                this
            )
    }

    protected override onAutoObserver(): void {
        this.addReaction(
            () => store.game.roomData.roundInfo?.currentPlayerCursor,
            (cursor) => {
                this.updateColor(cursor)
            },
            { fireImmediately: true }
        )

        this.addAutorun([
            () => {
                if (this.is_steal_card) {
                    this.updateHandCardSelected(
                        store.game.stealSelectedCardIndex
                    )
                } else {
                    this.updateHandCardSelected(store.game.selectedCardIndex)
                }
            },
        ])
    }

    private onStealSelectedCardHandler(selectedCardIndex: number) {
        cat.ws.Request(
            'DataBroadcast',
            reflect(
                ClientDataBroadcastSchema,
                create(ClientDataBroadcastSchema, {
                    data: JSON.stringify({
                        selectedCardIndex,
                    }),
                })
            ),
            ClientDataBroadcastSchema
        )
        store.game.stealSelectedCardIndex = selectedCardIndex
    }

    private onSelectedCardHandler(selectedCardIndex: number) {
        if (store.game.selectedCardIndex === selectedCardIndex) {
            store.game.selectedCardIndex = undefined
        } else {
            store.game.selectedCardIndex = selectedCardIndex
            const card = this.list.children
                .find((item, index) => index === selectedCardIndex)
                ?.getComponent(LightCardItem)
            cat.event.dispatchEvent(GameEventConstant.SHOW_CARD_INFO, {
                card: card?.props?.card,
            })
        }
    }

    updateHandCardSelected(selectedCardIndex: number | undefined) {
        this.list.children.forEach((item, index) => {
            if (!item.isValid) {
                return
            }
            const card = item?.getComponent(LightCardItem)

            if (card) {
                card.setUpdateData({
                    is_selected: index === selectedCardIndex,
                })
            }
        })
    }
    /**更新手牌布局 */
    onUpdateHandCardLayout() {
        this.list.getComponent(Layout)!.updateLayout()
        const hand_card = this.list.getComponent(UITransform)!
        // 判断手牌容器是否超出限制
        window.ccLog(
            '------更新手牌布局------',
            hand_card.width >= this.limit,
            hand_card.width,
            this.limit
        )
        if (hand_card.width >= this.limit) {
            // 单张手牌宽度
            const once_card_width =
                this.list.children[0]?.getComponent(UITransform)!.width || 0
            // 手牌张数
            const len = this.list.children.length

            this.gap = (this.limit - once_card_width * len) / len - 1
        }
        this.list.getComponent(Layout)!.spacingX = this.gap

        this.updateColor()
        cat.event.dispatchEvent(GameEventConstant.AFTER_UPDATE_HANDCARD_LAYOUT)
        return this
    }

    /**更新手牌颜色 */
    updateColor(cursor?: number) {
        const currentCursor =
            cursor === undefined
                ? store.game.roomData.roundInfo?.currentPlayerCursor
                : cursor
        // 自己的操作回合
        window.ccLog('更新手牌颜色', currentCursor, store.user.userIndex)
        this.setHandCardGray(currentCursor != store.user.userIndex)
    }

    /**置灰手牌 */
    setHandCardGray(is_gray: boolean = true) {
        this.list.children.forEach((item) => {
            const card = item?.getComponent(LightCardItem)
            if (card?.isHandCard) {
                card.setUpdateData({ is_gray })
            }
        })
    }

    /**一次性添加手牌 */
    onceAddCard(cards: Card[]) {
        this.list.active = true
        cards.forEach((card) => {
            const node = instantiate(this.card_prefab)
            node.getComponent(LightCardItem)!
                .setHandCard(true)
                .addToParent(this.list, {
                    props: { card, is_steal_card: this.is_steal_card },
                })
            this.onUpdateHandCardLayout()
        })
        return this
    }

    /**清除手牌 */
    clearCard() {
        this.list.removeAllChildren()
        this.list.destroyAllChildren()
        return this
    }

    /**获取选中的手牌 */
    getCardSelected() {
        return this.list.children.find((item, index) => {
            return index === store.game.selectedCardIndex
        })
    }

    /**获取卡牌根据id */
    getCardById(id: Card) {
        window.ccLog('获取卡牌根据id', this.list)
        return this.list.children.filter((item) => {
            window.ccLog('card', item.getComponent(LightCardItem)!.props.card)
            return item.getComponent(LightCardItem)!.props.card == id
        })
    }

    getCardList(): Card[] {
        return this.list.children.map(
            (item) =>
                item.getComponent(LightCardItem)?.props?.card ||
                Card.UNSPECIFIED
        )
    }
}
