import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import {
    _decorator,
    instantiate,
    Tween,
    tween,
    UITransform,
    v3,
    Vec3,
    Node,
} from 'cc'
import { Desk } from './Desk'
import { Card } from '@/pb-generate/server/pirate/v1/card_pb'
import {
    AudioEffectConstant,
    GameEventConstant,
    GlobalEventConstant,
} from '@/core/business/constant'
import { CardChineseName } from '@/core/business/types/IGame'
import { cat } from '@/core/manager'
import { LightCardItem } from '../card/LightCardItem'
import { CardItem } from '../card/CardItem'
import { PlayerDrawnCardMessage } from '@/pb-generate/server/pirate/v1/player_pb'
import { sleep } from '@/core/business/util/TimeUtils'
import { gameVisibleCheck } from '@/core/business/hooks/Decorator'
const { ccclass, property } = _decorator

@ccclass('CardAnimationManager')
export default class CardAnimationManager extends BaseComponent {
    private desk: Desk

    @property({ type: Node, tooltip: '卡牌展示区后面的动画节点' })
    shine: Node = null!

    /**获取牌堆位置 */
    private drawZonePos: Vec3 = v3()
    /**抽牌后卡牌展示区位置 */
    private drawDisplayZonePos: Vec3 = v3()
    /**获取发牌区位置 */
    private playZonePos: Vec3 = v3()
    /**获取手牌位置 */
    private handZonePos: Vec3 = v3()

    // 发牌进行中
    public isDealCardLoading: boolean = false
    override initUI(): void {
        this.desk = this.getComponent(Desk)!
    }

    /**
     * 将世界坐标转换为card_move_tween节点下的本地坐标
     */
    private convertToCardMoveTweenSpace(worldPosition: Vec3): Vec3 {
        return this.desk.card_move_tween
            .getComponent(UITransform)!
            .convertToNodeSpaceAR(worldPosition)
    }

    override start(): void {
        // 获取牌堆和发牌区位置
        this.drawZonePos = this.convertToCardMoveTweenSpace(
            this.desk.draw_zone.worldPosition
        )
        this.drawDisplayZonePos = this.convertToCardMoveTweenSpace(
            this.desk.draw_display_zone.worldPosition
        )
        this.playZonePos = this.convertToCardMoveTweenSpace(
            this.desk.play_zone.worldPosition
        )
        // window.ccLog('获取牌堆和发牌区位置:', this.drawZonePos, this.playZonePos)
        // 获取手牌位置
        this.handZonePos = this.convertToCardMoveTweenSpace(
            this.desk.hand_card.list.worldPosition
        )
        // window.ccLog('获取手牌位置:', this.handZonePos)
    }

    protected override onEventListener(): void {
        cat.event
            .on(
                GameEventConstant.AFTER_UPDATE_HANDCARD_LAYOUT,
                this.onAfterUpdateHandCardLayout,
                this
            )
            .on(
                GlobalEventConstant.EVENT_HIDE,
                this.onHandleAppBackground,
                this
            )
    }

    private onHandleAppBackground() {
        this.shine.active = false

        this.clearCardMoveTween()
    }

    private onAfterUpdateHandCardLayout() {
        if (this.desk.hand_card.list.children.length) {
            this.handZonePos = this.convertToCardMoveTweenSpace(
                this.desk.hand_card.list.children[
                    this.desk.hand_card.list.children.length - 1
                ].worldPosition
            )
        }
    }

    /**发牌动画 */
    async dealCard(cards: Card[], is_tween: boolean = true) {
        if (this.isDealCardLoading || !cards.length) {
            return
        }
        this.clearCardMoveTween()
        this.isDealCardLoading = true
        window.ccLog('发牌', [...cards])
        this.desk.hand_card.clearCard()
        // 发牌动画时间
        const duration = 0.2
        // 所有发牌动画
        const dealCardTweenPromise: Promise<void>[] = []
        if (is_tween) await cat.audio.playEffect(AudioEffectConstant.DEAL_CARD)
        let nonce: string[] = []
        cards.forEach((card, index) => {
            nonce.push(CardChineseName[card])
            dealCardTweenPromise.push(
                this.onceDealAnimation({
                    card,
                    duration: duration,
                    delay: 100 * index,
                    is_tween,
                })
            )
        })
        Promise.all(dealCardTweenPromise).finally(() => {
            cat.tracking.game.cardsFinish(nonce)
            this.isDealCardLoading = false
            this.clearCardMoveTween()
        })
    }

    /**
     * 单张发牌动画
     * @param card - 要发的牌
     * @param duration - 动画持续时间，默认0.2秒
     * @param is_tween - 是否播放动画
     */
    async onceDealAnimation({
        card,
        duration = 0.2,
        delay = 0,
        is_tween,
    }: {
        card: Card
        duration?: number
        delay?: number
        is_tween: boolean
    }) {
        if (is_tween) {
            await sleep(delay)
        }
        // 从对象池获取卡牌节点
        const hand_card_item_node = this.desk.cardPool?.get()
        // 获取卡牌组件
        const cardItem = hand_card_item_node?.getComponent(LightCardItem)!

        // 将卡牌添加到移动动画层并设置初始位置
        cardItem
            ?.addToParent(this.desk.card_move_tween, { props: { card } })
            ?.node.setPosition(this.drawZonePos)
        // 如果需要动画效果，则播放移动动画
        if (is_tween) {
            await cardItem?.moveTween(duration, { position: this.handZonePos })
        }
        // 设置为手牌状态
        cardItem?.setHandCard(true)
        // 将卡牌移动到手牌区
        hand_card_item_node?.setParent(this.desk.hand_card.list, true)

        // 刷新手牌布局
        cat.event.dispatchEvent(GameEventConstant.UPDATE_HANDCARD_LAYOUT)
    }

    /**单张出牌动画 */
    @gameVisibleCheck()
    selfPlay(handCardIndices: number[], duration: number = 0.3) {
        // 清除移动动画层中的卡牌
        this.clearCardMoveTween()

        // 遍历手牌区域中的所有卡牌
        this.desk.hand_card.list.children.forEach((node, index) => {
            // 如果当前卡牌索引在要打出的卡牌索引数组中
            if (handCardIndices.includes(index)) {
                // 获取卡牌在世界坐标系中的位置，并转换为移动动画层的本地坐标
                const startPosition = this.convertToCardMoveTweenSpace(
                    node.worldPosition
                )

                // 获取卡牌组件，将其添加到移动动画层，设置初始位置
                // 执行移动动画：从当前位置移动到出牌区域，并设置缩放比例为1
                node.getComponent(LightCardItem)!
                    .setUpdateData({ is_selected: false })
                    ?.setParent(this.desk.card_move_tween)
                    .setPosition(startPosition)
                    .moveTween(duration, {
                        position: this.playZonePos,
                        scale: Vec3.ONE,
                    })
                    .then(() => {
                        // 动画完成后，将卡牌设置为非手牌状态
                        // 并将其移动到出牌区域
                        node.getComponent(LightCardItem)
                            ?.setHandCard(false)
                            .setParent(this.desk.play_zone, true)
                    })
            }
        })
    }

    /**
     * 其他人出牌
     * @param player - 出牌的玩家节点
     * @param cards - 打出的卡牌数组
     * @param duration - 动画持续时间，默认0.2秒
     */
    @gameVisibleCheck()
    async otherPlay({
        player,
        cards,
        duration = 0.2,
    }: {
        player: Node
        cards: Card[]
        duration?: number
    }) {
        // 清除移动动画层中的卡牌
        this.clearCardMoveTween()
        // 获取第一张卡牌
        const card = cards[0]
        // 获取玩家位置并转换为动画层坐标
        const startPos = this.convertToCardMoveTweenSpace(player.worldPosition)
        // 从对象池获取卡牌节点
        const light_card_node = this.desk.cardPool?.get()!
        // 获取卡牌组件
        const cardItem = light_card_node.getComponent(LightCardItem)!
        // 设置卡牌初始状态
        cardItem
            .addToParent(this.desk.card_move_tween, { props: { card } })
            .setPosition(startPos)
            .node.setScale(Vec3.ZERO)
        // 执行移动动画并返回Promise
        return cardItem
            .moveTween(duration, {
                position: this.playZonePos,
                scale: Vec3.ONE,
            })
            .then(() => {
                // 动画完成后，将卡牌移动到出牌区
                light_card_node.setParent(this.desk.play_zone, true)
            })
    }

    /**
     * 抽牌动画
     * @param data - 玩家抽牌消息
     * @description
     * 1. 清除移动动画层中的卡牌
     * 2. 从对象池获取卡牌节点
     * 3. 设置卡牌初始位置和层级
     * 4. 执行三段动画:
     *    - t1: 从牌堆移动到展示区，并显示光效
     *    - t2: 展示区停留0.5秒
     *    - t3: 从展示区移动到手牌区
     */
    @gameVisibleCheck()
    drawTween(data: PlayerDrawnCardMessage) {
        this.clearCardMoveTween()
        // 动画 从shark到card
        const light_card_node = this.desk.cardPool?.get()!

        const start = this.drawZonePos

        // 设置卡牌初始状态
        light_card_node
            .getComponent(LightCardItem)
            ?.setNodeAndChildrenLayer('DRAW')
            .setPosition(start)
            .addToParent(this.desk.card_move_tween, {
                props: {
                    card: data.drawnCard,
                },
            })

        // 第一段动画：移动到展示区并显示光效
        const t1 = tween(light_card_node)
            .to(0.3, {
                position: this.drawDisplayZonePos.clone(),
            })
            .call(() => {
                this.shine.active = true
                cat.audio.playEffect(AudioEffectConstant.GET_CARD)
            })

        // 第二段动画：展示区停留
        const t2 = tween(light_card_node)
            .delay(0.8)
            .call(() => {
                this.shine.active = false
            })

        // 第三段动画：移动到手牌区
        const t3 = tween(light_card_node)
            .to(0.3, {
                position: this.handZonePos,
            })
            .call(() => {
                light_card_node
                    .getComponent(LightCardItem)
                    ?.setNodeAndChildrenLayer('Game UI')
                    ?.setParent(this.desk.hand_card.list, true)
                    .setHandCard(true)
                this.desk.hand_card.onUpdateHandCardLayout()
            })

        // 依次执行三段动画
        tween(light_card_node).then(t1).then(t2).then(t3).start()
    }
    /**
     * 其他人抽牌
     * @param player - 抽牌的玩家节点
     * @param duration - 动画持续时间，默认0.5秒
     */
    @gameVisibleCheck()
    async otherDrawTween({
        player,
        duration = 0.5,
    }: {
        player: Node
        duration?: number
    }) {
        // 获取玩家位置并转换为动画层坐标系
        const player_pos = this.desk.card_move_tween
            .getComponent(UITransform)!
            .convertToNodeSpaceAR(player.worldPosition)

        // 从对象池获取卡牌节点
        const hand_card_item_node = this.desk.cardPool?.get()

        // 获取卡牌组件
        const cardItem = hand_card_item_node?.getComponent(LightCardItem)
        if (!cardItem) {
            return
        }
        // 设置卡牌初始状态：添加到动画层、设置位置和缩放
        cardItem
            .addToParent(this.desk.card_move_tween, {
                props: {
                    card: Card.UNSPECIFIED,
                },
            })
            .setPosition(this.drawZonePos)
            .setScale(Vec3.ZERO)

        // 第一段动画：从牌堆位置缩放显示
        await cardItem.moveTween(0.5, { scale: Vec3.ONE })
        // 第二段动画：移动到玩家位置并缩小消失
        await cardItem.moveTween(duration, {
            position: player_pos,
            scale: Vec3.ZERO,
        })
        // 清理动画层中的卡牌
        this.clearCardMoveTween()
    }

    /**
     * 索取偷牌动效
     * @param from - 被偷牌的玩家节点
     * @param card - 被偷的卡牌
     * @param duration - 动画持续时间，默认0.3秒
     * @description
     * 1. 清除移动动画层中的卡牌
     * 2. 从对象池获取卡牌节点
     * 3. 设置卡牌初始位置和缩放
     * 4. 执行动画:从被偷玩家位置移动到自己手牌区
     */
    @gameVisibleCheck()
    async stealTween({
        from,
        card,
        duration = 0.3,
    }: {
        from: Node
        card: Card
        duration?: number
    }) {
        // 清除动画层中的卡牌
        this.clearCardMoveTween()
        // 从对象池获取卡牌节点
        const light_card_node = this.desk.cardPool?.get()!

        // 获取被偷玩家位置并转换为动画层坐标
        const start = this.convertToCardMoveTweenSpace(from.worldPosition)

        // 设置卡牌初始状态：添加到动画层、设置位置和缩放
        light_card_node
            .getComponent(LightCardItem)!
            .setPosition(start)
            .setScale(Vec3.ONE)
            .addToParent(this.desk.card_move_tween, {
                props: {
                    card,
                },
            })

        // 创建移动动画：从被偷玩家位置到手牌区
        const t3 = tween(light_card_node)
            .to(duration, {
                position: this.handZonePos,
            })
            .call(() => {
                // 动画完成后，设置卡牌状态并更新手牌布局
                light_card_node
                    .getComponent(LightCardItem)
                    ?.setNodeAndChildrenLayer('Game UI')
                    ?.setParent(this.desk.hand_card.list, true)
                    .setScale(Vec3.ONE)
                    .setHandCard(true)
                this.desk.hand_card.onUpdateHandCardLayout()
            })

        // 执行动画
        tween(light_card_node).then(t3).start()
    }

    /**
     * 被索取者->索取者的动画效果
     * @param to - 索取者节点
     * @param targetPlayerHandCardIndices - 被索取的手牌索引数组
     * @param duration - 动画持续时间，默认0.3秒
     * @description
     * 1. 清除移动动画层中的卡牌
     * 2. 获取索取者位置
     * 3. 遍历手牌，找到被索取的卡牌
     * 4. 执行动画：从当前位置移动到索取者位置并缩小消失
     * 5. 动画结束后将卡牌放回对象池
     */
    @gameVisibleCheck()
    async stealFromTween({
        to,
        targetPlayerHandCardIndices,
        duration = 0.3,
    }: {
        to: Node
        targetPlayerHandCardIndices: number[]
        duration?: number
    }) {
        this.clearCardMoveTween()
        const toPlayerPosition = this.convertToCardMoveTweenSpace(
            to.worldPosition
        )

        this.desk.hand_card.list.children.forEach((node, index) => {
            if (targetPlayerHandCardIndices.includes(index)) {
                const startPosition = this.desk.card_move_tween
                    .getComponent(UITransform)!
                    .convertToNodeSpaceAR(node.worldPosition)

                node.getComponent(LightCardItem)!
                    ?.setParent(this.desk.card_move_tween)
                    .setPosition(startPosition)
                    .moveTween(duration, {
                        position: toPlayerPosition,
                        scale: Vec3.ZERO,
                    })
            }
        })
    }

    /**
     * 其他玩家索取偷牌动效
     * @param from - 被偷牌的玩家节点
     * @param to - 偷牌的玩家节点
     * @param duration - 动画持续时间，默认0.3秒
     * @description
     * 1. 获取被偷玩家和偷牌玩家的位置坐标
     * 2. 从对象池获取卡牌节点
     * 3. 设置卡牌初始位置和缩放
     * 4. 执行动画：从被偷玩家位置移动到偷牌玩家位置并缩小消失
     * 5. 清理动画层中的卡牌
     */
    @gameVisibleCheck()
    async otherStealTween({
        from,
        to,
        duration = 0.3,
    }: {
        from: Node
        to: Node
        duration?: number
    }) {
        const fromPlayerPosition = this.convertToCardMoveTweenSpace(
            from.worldPosition
        )
        const toPlayerPosition = this.convertToCardMoveTweenSpace(
            to.worldPosition
        )
        const hand_card_item_node = this.desk.cardPool?.get()!
        const cardItem = hand_card_item_node.getComponent(LightCardItem)!
        cardItem
            .addToParent(this.desk.card_move_tween, {
                props: {
                    card: Card.UNSPECIFIED,
                },
            })
            .setPosition(fromPlayerPosition)
            .setScale(v3(0.5, 0.5, 1))

        await cardItem.moveTween(duration, {
            position: toPlayerPosition,
            scale: Vec3.ZERO,
        })
        this.clearCardMoveTween()
    }

    // 当前玩家交换其他玩家手牌
    // 1. 清除动画层中的卡牌
    // 2. 将自己的手牌移动到目标玩家的手牌区
    // 3. 目标玩家的手牌移动到自己的手牌区
    @gameVisibleCheck()
    async swapTween({
        targetPlayer,
        cards,
        duration = 0.3,
    }: {
        targetPlayer: Node
        cards: Card[]
        duration?: number
    }) {
        // 获取目标玩家位置
        const targetPlayerPosition = this.convertToCardMoveTweenSpace(
            targetPlayer.worldPosition
        )

        // 创建两个动画组：自己的手牌移动到目标玩家，目标玩家的手牌移动到自己
        const selfToTargetPromises: Promise<void>[] = []
        const targetToSelfPromises: Promise<void>[] = []

        // 1. 自己的手牌移动到目标玩家
        for (
            let i = this.desk.hand_card.list.children.length - 1;
            i >= 0;
            i--
        ) {
            const node = this.desk.hand_card.list.children[i]
            const startPosition = this.convertToCardMoveTweenSpace(
                node.worldPosition
            )

            const cardItem = node?.getComponent(LightCardItem)
            if (!cardItem) {
                console.warn(
                    `Failed to get LightCardItem component for hand card ${i}`
                )
                continue
            }

            selfToTargetPromises.push(
                cardItem
                    .addToParent(this.desk.card_move_tween)
                    .setPosition(startPosition)
                    .moveTween(duration, {
                        position: targetPlayerPosition,
                        scale: v3(0.5, 0.5, 1),
                    })
                    .then(() => {
                        this.desk.cardPool?.put(node)
                    })
            )
        }

        // 2. 目标玩家的手牌移动到自己
        // 为每张目标玩家的手牌创建动画
        this.handZonePos = this.convertToCardMoveTweenSpace(
            this.desk.hand_card.list.worldPosition
        )

        for (let card of cards) {
            // 从对象池获取卡牌节点
            const cardNode = this.desk.cardPool?.get()!

            const cardItem = cardNode.getComponent(LightCardItem)

            if (!cardItem) {
                console.error('Failed to get LightCardItem component')
                continue
            }

            targetToSelfPromises.push(
                cardItem
                    .addToParent(this.desk.card_move_tween, {
                        props: { card },
                    })
                    .setPosition(targetPlayerPosition)
                    .setScale(Vec3.ZERO)
                    .moveTween(duration, {
                        position: this.handZonePos,
                        scale: Vec3.ONE,
                    })
                    .then(() => {
                        // 动画完成后，将卡牌添加到手牌区
                        cardItem
                            .setParent(this.desk.hand_card.list, true)
                            .setHandCard(true)
                    })
            )
        }
        await Promise.all([...targetToSelfPromises, ...selfToTargetPromises])

        // 清理动画层中的卡牌，更新手牌布局
        this.clearCardMoveTween()
        this.desk.hand_card.onUpdateHandCardLayout()
    }

    clearCardMoveTween() {
        this.desk.card_move_tween?.children.forEach((node) => {
            Tween.stopAllByTarget(node)
            this.desk.cardPool?.put(node)
        })
    }

    clearPlayZone() {
        this.desk.play_zone?.children.forEach((node) => {
            this.desk.cardPool?.put(node)
        })
    }
}
