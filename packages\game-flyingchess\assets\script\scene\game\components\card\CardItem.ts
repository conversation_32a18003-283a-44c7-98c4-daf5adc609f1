import {
    __private,
    _decorator,
    CCFloat,
    Component,
    Label,
    math,
    Node,
    Sprite,
    Sprite<PERSON>rame,
    tween,
    Tween,
    v3,
    Vec3,
} from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
const { ccclass, property } = _decorator

@ccclass('CardItem')
export class CardItem<
    T extends Object = {},
    K extends Object = {}
> extends BaseComponent<T, K> {
    @property({ type: Node, tooltip: '卡牌节点' })
    card: Node = null!

    @property({ type: CCFloat, tooltip: '选中时放大倍率' })
    selected_scale = 1.1

    @property({ type: CCFloat, tooltip: '选中动画持续时间(s)' })
    selected_duration = 0.1

    /**是否在运动(运动中禁止选择) */
    isTween: boolean = true

    /**是否有效(已经打牌的牌为无效牌) */
    isValidCard: boolean = true

    /**是否为手牌 */
    isHandCard: boolean = false

    override onDestroy(): void {
        Tween.stopAllByTarget(this.node)
    }

    /**飞行动画 */
    moveTween(
        duration: number,
        props: __private._cocos_tween_tween__ConstructorType<Node>,
        node: Node = this.node
    ) {
        return new Promise<void>((resolve) => {
            this.isTween = true
            tween(node)
                .to(duration, props)
                .call(() => {
                    Tween.stopAllByTarget(node)
                    this.isTween = false
                    resolve()
                })
                .start()
        })
    }

    protected get currentIndex(): number | undefined {
        return this.node.parent?.children?.indexOf(this.node)
    }
}
