import {
    _decorator,
    Component,
    instantiate,
    isValid,
    Label,
    log,
    Node,
    Prefab,
    Animation,
    Skeleton,
    sp,
    tween,
    v3,
    Tween,
    Vec3,
    Sprite<PERSON>rame,
    Sprite,
    ProgressBar,
} from 'cc'
import { SocketEvent } from '@/core/business/ws'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import {
    AudioEffectConstant,
    GameEventConstant,
} from '@/core/business/constant'
import { cat } from '@/core/manager'
import { create } from '@bufbuild/protobuf'
import store from '@/core/business/store'
import { Player, PlayerSchema } from '@/pb-generate/server/pirate/v1/player_pb'

import { PlayerItem } from './PlayerItem'

import { State as DeskState } from '@/pb-generate/server/pirate/v1/state_pb'
import GetTimeDifferenceFromServer from '@/core/business/hooks/GetTimeDifferenceFromServer'

const { ccclass, property } = _decorator

/**回合状态 */
export enum RoundState {
    /** 正常 */
    NORMAL = 0,
    /** 倒计时 */
    COUNTDOWN,
}

@ccclass('Countdown')
export class Countdown extends BaseComponent {
    @property({ type: Sprite, tooltip: '玩家节点' })
    player_node: Sprite = null!

    @property({ type: Sprite, tooltip: '操作倒计时节点' })
    countdown_node: Sprite = null!

    @property({ type: [SpriteFrame], tooltip: '边框精灵图集' })
    border_spriteFrame: SpriteFrame[] = []

    @property({ type: Sprite, tooltip: '边框' })
    border: Sprite = null!

    @property({ type: ProgressBar, tooltip: '剩余时间进度' })
    time_progres: ProgressBar

    @property({ type: Label, tooltip: '剩余时间' })
    time_count: Label

    /**计时开关 */
    private time_switch: boolean = false

    /**结束时间 */
    private end_time: number = 0

    /**运行时间 */
    private run_time: number = 0

    /**运行长度 */
    private duration: number = 0

    override props = {
        player: create(PlayerSchema),
    }

    protected override onEventListener(): void {
        cat.event.on(
            GameEventConstant.STOP_PLAYER_Timer,
            this.onStopPlayerTimerHandler,
            this
        )
    }

    protected override onAutoObserver(): void {
        this.addAutorun([
            () => {
                const state = store.game.roomData.stateInfo?.state!
                const cursor =
                    store.game.roomData.roundInfo?.currentPlayerCursor!
                const timeoutSec = store.game.roomData.stateInfo?.countdownMs
                if (
                    ![
                        DeskState.GAME_START,
                        DeskState.GAME_FINISHING,
                        DeskState.GAME_FINISHED,
                        DeskState.UNSPECIFIED,
                    ].includes(state)
                ) {
                    if (this.isCurrentPlayer(cursor)) {
                        window.ccLog(`更新指定玩家${cursor}计时`, timeoutSec)
                        this.updateActionTime(
                            GetTimeDifferenceFromServer(
                                Number(
                                    store.game.roomData.stateInfo
                                        ?.serverTimestamp
                                ) + Number(timeoutSec)
                            )
                        )
                    } else {
                        // 其他玩家清除计时
                        // window.ccLog('其他玩家清除计时')
                        this.stopTimer()
                    }
                } else {
                    // window.ccLog('其他状态清除计时')
                    this.stopTimer()
                }
            },
        ])
    }

    protected override update(dt: number): void {
        if (this.time_switch) {
            this.run_time += dt * 1_000
            if (this.run_time >= this.end_time) {
                //计时结束
                // window.ccLog('计时结束清楚计时', this.run_time, this.end_time)
                // 停止计时
                this.run_time = this.end_time
                this.stopTimer()
            }

            const time = Math.round((this.end_time - this.run_time) / 1_000)
            const fillRange = (this.end_time - this.run_time) / this.duration
            this.countdown_node.fillRange = this.time_progres.progress =
                fillRange

            this.time_count.string = `${time}s`
            // 当前玩家是用户
            if (this.props.player.id === store.game.getUserPlayer?.id) {
                cat.event.dispatchEvent(GameEventConstant.DRAW_SCOUT_TIME_OUT, {
                    time,
                    progress: fillRange,
                })
            }
        }
    }

    /**开始计时 */
    private startTimer(end: number) {
        const now = Date.now()
        this.run_time = now
        this.end_time = end
        this.duration = end - now
        this.time_switch = true
        this.countdown_node.node.active = true
    }

    /**停止计时 */
    private stopTimer() {
        this.time_switch = false
        this.changePlayerRonudSatate(RoundState.NORMAL)
        this.countdown_node.node.active = false
    }

    /**
     * 更新操作时间
     * @param endtime 回合结束时间戳
     */
    private updateActionTime(endtime: number) {
        // 切换状态
        this.changePlayerRonudSatate(RoundState.COUNTDOWN)
        // 开始计时
        this.startTimer(endtime)
    }

    /**停止玩家计时操作处理 */
    private onStopPlayerTimerHandler(index: number) {
        if (index == this.props.player.index) {
            this.stopTimer()
        }
    }

    /**切换玩家回合状态 */
    private changePlayerRonudSatate(state: RoundState) {
        Tween.stopAllByTarget(this.player_node.node)
        if (state == RoundState.NORMAL) {
            this.player_node.node.setScale(Vec3.ONE)
            this.border.spriteFrame = this.border_spriteFrame[0]
            this.countdown_node.node.active = false
        } else {
            this.player_node.node.setScale(1.1, 1.1)
            this.border.spriteFrame = this.border_spriteFrame[1]
            this.countdown_node.node.active = true
            // 放大效果 1->1.2 -> 1
            tween(this.node)
                .to(0.2, {
                    scale: v3(1.2, 1.2, 1),
                })
                .to(0.2, {
                    scale: v3(1, 1, 1),
                })
                .start()
        }
    }

    /**index是当前玩家 */
    protected isCurrentPlayer(index: number) {
        return this.props.player.index === index
    }

    /**操作者是当前用户 */
    protected isCurrentCursor(cursor: number) {
        return store.user.userIndex == cursor
    }
}
