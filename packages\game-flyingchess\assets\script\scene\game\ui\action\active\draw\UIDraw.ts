import {
    _decorator,
    Button,
    Camera,
    Component,
    instantiate,
    isValid,
    Label,
    Layers,
    log,
    Node,
    Prefab,
    ProgressBar,
    Quat,
    Sprite,
    SpriteFrame,
    Tween,
    tween,
    UITransform,
    v3,
    Vec3,
} from 'cc'
import { create } from '@bufbuild/protobuf'
import { reflect } from '@bufbuild/protobuf/reflect'
// import {
//     CardState,
//     DataBroadcastRequestSchema,
//     DataBroadcastResponseSchema,
//     Draw,
//     DrawCardRequestSchema,
//     DrawCardResponseSchema,
// } from '@/pb-generate/server/pirate/v1/message_pb'
import { Card, CardState } from '@/pb-generate/server/pirate/v1/card_pb'
import { State as DeskState } from '@/pb-generate/server/pirate/v1/state_pb'
import { SocketEvent } from '@/core/business/ws'
import { TimeCount } from '@/script/scene/game/components/time-count/TimeCount'
import { LightCardItem } from '@/script/scene/game/components/card/LightCardItem'
import { HandCard } from '@/script/scene/game/components/desk/HandCard'
import UILayer from '@/core/manager/gui/layer/UILayer'
import { cat } from '@/core/manager'
import store from '@/core/business/store'
import { audioEffect } from '@/core/business/hooks/Decorator'
import { Barrel, ZoomState } from '@/script/scene/game/components/barrel/Barrel'
import {
    AudioEffectConstant,
    GameEventConstant,
} from '@/core/business/constant'
import { JSBridgeClient } from '@/core/business/jsbridge/JSBridge'
import { PlayerDrawnCardMessage } from '@/pb-generate/server/pirate/v1/player_pb'
import { ClientDataBroadcastSchema } from '@/pb-generate/server/pirate/v1/game_pb'
import { DrawCardRequestSchema } from '@/pb-generate/server/pirate/v1/handler_pb'
import { KnifeHole } from '@/script/scene/game/components/barrel/KnifeHole'
import { sleep } from '@/core/business/util/TimeUtils'

const { ccclass, property } = _decorator

type UIDrawProps = {
    barrel: Barrel | null
    camera3D: Camera | null
    /**手牌节点 */
    // hand_card: HandCard
}

@ccclass('UIDraw')
export class UIDraw extends UILayer<UIDrawProps> {
    @property({ type: Button, tooltip: '取消按钮' })
    btn_cancel: Button

    @property({ type: Node, tooltip: '超时节点' })
    overtime: Node

    @property({ type: Button, tooltip: '确定按钮' })
    btn_confirm: Button

    @property({ type: Label, tooltip: '剩余牌(牙)' })
    remaining_count: Label

    @property({ type: Label, tooltip: '危险牌(牙)' })
    boom_count: Label

    @property({ type: Node, tooltip: '桌子牌堆信息' })
    desk_card_info: Node

    @property({ type: TimeCount, tooltip: '倒计时时间' })
    time_count: TimeCount

    @property({ type: Node, tooltip: '提示节点' })
    tips: Node

    @property({ type: [SpriteFrame], tooltip: '提示节点精灵图' })
    tips_spriteFrame: SpriteFrame[] = []
    // @property({ type: Label, tooltip: '提示节点Label' })
    // tips_label: Label

    @property({ type: Node, tooltip: '主要容器节点' })
    main: Node

    private light_card_node: Node | null = null

    override props: UIDrawProps = {
        barrel: null,
        camera3D: null,
        // hand_card: null!,
    }

    protected override onLoad(): void {
        window.ccLog('UIDraw onLoad ----------------')
        JSBridgeClient.disableTouch(true)
        store.game.layerStatus.draw = true
        this.btn_confirm.node.on(
            Button.EventType.CLICK,
            this.onConfirmHandler,
            this
        )
        this.btn_cancel.node.on(
            Button.EventType.CLICK,
            this.onCancelHandler,
            this
        )
    }

    protected override initUI(): void {}

    protected override onEventListener(): void {
        cat.event.on<SocketEvent>(
            'MT_PLAYER_DRAWN_CARD_MESSAGE',
            this.onDrawBroadcastHandler,
            this
        )
    }

    protected override start(): void {
        window.ccLog('UIDraw start ----------------')
        store.game.selected_tooth = []

        store.game.click_tooth_type = 1
        store.game.is_allow_click_tooth = true

        this.tips.getComponent(Sprite)!.spriteFrame =
            this.tips_spriteFrame[store.game.getPlayerDrawCount - 1]
    }

    protected override onAutoObserver(): void {
        this.addAutorun([
            () => {
                this.remaining_count.string = `${store.game.roomData.deckInfo?.deckRemainingCardCount}`
            },
            () => {
                this.boom_count.string = `${store.game.roomData.deckInfo?.deckRemainingDeathCardCount}`
            },

            () => {
                this.overtime.active =
                    this.desk_card_info.active =
                    this.time_count.node.active =
                    this.btn_cancel.node.active =
                    this.btn_confirm.node.active =
                    this.tips.active =
                        false
                switch (store.game.roomData.stateInfo?.state) {
                    case DeskState.PLAYER_DRAW_OR_POST:
                        this.desk_card_info.active =
                            this.time_count.node.active =
                            this.btn_cancel.node.active =
                            this.btn_confirm.node.active =
                            this.tips.active =
                                true
                        break
                    case DeskState.PLAYER_HOSTING:
                        this.desk_card_info.active = this.overtime.active = true
                        break

                    default:
                        break
                }
            },
            () => {
                if (
                    store.game.roomData.stateInfo?.state ===
                        DeskState.PLAYER_DRAW_OR_POST &&
                    store.game.roomData.roundInfo?.currentPlayerCursor !==
                        store.user.userIndex
                ) {
                    this.close()
                }
            },

            // 修复中了诅咒和多个锅的状态下摸牌后鲨鱼不收起
            () => {
                const state = store.game.roomData.stateInfo?.state
                const cursor =
                    store.game.roomData.roundInfo?.currentPlayerCursor

                const lastState = store.game.lastRoomData.stateInfo?.state
                const lastCursor =
                    store.game.lastRoomData.roundInfo?.currentPlayerCursor

                if (
                    lastState === DeskState.PLAYER_NEXT_CONSUME_DRAWN &&
                    state === DeskState.PLAYER_DRAW_OR_POST &&
                    cursor === store.user.userIndex &&
                    lastCursor === store.user.userIndex
                ) {
                    store.game.lastRoomData = store.game.roomData
                    this.close()
                }
            },
        ])

        this.addReaction(
            () => store.game.roomData.stateInfo?.state,
            (state) => {
                if (state === DeskState.PLAYER_HOSTING) {
                    // 显示系统摸牌
                    this.overtime.active = true
                }
            }
        )
    }

    protected override onDestroy(): void {
        JSBridgeClient.disableTouch(false)
        this.unscheduleAllCallbacks()
        if (this.light_card_node && isValid(this.light_card_node)) {
            Tween.stopAllByTarget(this.light_card_node)
        }
    }

    /**抽牌广播 */
    private async onDrawBroadcastHandler(data: PlayerDrawnCardMessage) {
        store.game.is_allow_click_tooth = false
        window.ccLog('UIDraw --> onDrawBroadcastHandler enter:', data)
        window.ccLog('Deskstate:', store.game.roomData.stateInfo?.state)

        // 超时系统摸牌
        // 改变牙齿状态+移动鲨鱼
        const state =
            data.drawnCard === Card.DEATH ? CardState.BAD : CardState.GOOD
        cat.event.dispatchEvent(GameEventConstant.TAG_TOOTH_STATE, {
            index: data.drawnDeckCardIndex,
            state,
        })

        await sleep(1500)
        if (data.remainingDrawnCards > 0 && data.drawnCard !== Card.DEATH) {
            return
        }
        this.close()
    }

    @audioEffect()
    private onConfirmHandler() {
        if (
            store.game.selected_tooth.length === store.game.getPlayerDrawCount
        ) {
            store.game.click_tooth_type = 0
            cat.ws
                .Request(
                    'DrawCard',
                    reflect(
                        DrawCardRequestSchema,
                        create(DrawCardRequestSchema, {
                            deckCardIndices: store.game.selected_tooth.map(
                                (item) =>
                                    item.getComponent(KnifeHole)?.props.index!
                            ),
                        })
                    ),
                    DrawCardRequestSchema
                )
                .then(() => {
                    // this.hideActionButton()
                    // // audio.playEffect(AudioEffectConstant.DRAW_POST_CARD)
                    // // 停止计时器
                    // cat.event.dispatchEvent(GameEventConstant.STOP_PLAYER_Timer, store.user.userIndex)
                })
        } else {
            cat.gui.showToast({
                title: `请选择${store.game.getPlayerDrawCount}处缝隙`,
            })
        }
    }

    @audioEffect()
    private onCancelHandler() {
        // 清空选择
        cat.ws.Request(
            'DataBroadcast',
            reflect(
                ClientDataBroadcastSchema,
                create(ClientDataBroadcastSchema, {
                    data: JSON.stringify({
                        selected: [],
                    }),
                })
            ),
            ClientDataBroadcastSchema
        )
        this.close()
    }

    private close() {
        // 强化状态时，还有牌未结算
        // if (
        //     store.game.roomData.stateInfo?.state &&
        //     [
        //         DeskState.PLAYER_NEXT_CONSUME_DRAWN,
        //         DeskState.PLAYER_PRE_NEXT_CONSUME_DRAWN,
        //     ].includes(store.game.roomData.stateInfo.state)
        // ) {
        //     return
        // }
        window.ccLog('UIDraw close ----------------')

        store.game.selectedCardIndex = undefined
        store.game.layerStatus.draw = false
        cat.gui.closeUI(this)
    }
}
