import {
    _decorator,
    Camera,
    Component,
    EventTouch,
    geometry,
    log,
    math,
    Node,
    PhysicsSystem,
    Quat,
    tween,
    v3,
    Vec2,
    Vec3,
} from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { cat } from '@/core/manager'
import {
    AudioEffectConstant,
    GameEventConstant,
} from '@/core/business/constant'
import { reflect } from '@bufbuild/protobuf/reflect'
import {
    DataBroadcastRequestSchema,
    DataBroadcastResponseSchema,
} from '@/pb-generate/server/pirate/v1/handler_pb'
import { create } from '@bufbuild/protobuf'
import { SocketEvent } from '@/core/business/ws'
import {
    PlayerDrawnCardMessage,
    PlayerDrawnDeathBroadcast,
} from '@/pb-generate/server/pirate/v1/player_pb'
import { Card } from '@/pb-generate/server/pirate/v1/card_pb'
import { sleep } from '@/core/business/util/TimeUtils'
import store from '@/core/business/store'

const { ccclass, property } = _decorator

@ccclass('Pirate')
export class Pirate extends BaseComponent {
    @property({ type: Node, tooltip: '海盗模型节点' })
    haidao_idle: Node = null!

    @property({ type: Node, tooltip: '海盗模型节点(被攻击前)' })
    haidao_jingkong: Node = null!

    @property({ type: Node, tooltip: '海盗模型节点（没有攻击到弱点）' })
    haidao_liuhan: Node = null!

    @property({ type: Node, tooltip: '海盗模型节点（攻击到弱点）' })
    haidao_xuanyun: Node = null!

    // 不再需要位置信息，因为使用固定位置

    private isGameOver = false

    protected override onLoad(): void {}

    protected override onEventListener(): void {
        cat.event
            .on<SocketEvent>(
                'MT_PLAYER_DRAWN_CARD_MESSAGE',
                this.onDrawBroadcastHandler,
                this
            )
            .on<SocketEvent>(
                'MT_CARD_DRAWN_DEATH_BROADCAST',
                this.onDrawDeathBroadcastHandler,
                this
            )
    }

    private showHaidaoIdle() {
        if (
            !this.haidao_idle ||
            !this.haidao_jingkong ||
            !this.haidao_liuhan ||
            !this.haidao_xuanyun
        )
            return
        this.haidao_idle.active = true
        this.haidao_jingkong.active =
            this.haidao_liuhan.active =
            this.haidao_xuanyun.active =
                false
    }

    private showHaidaoJingkong() {
        if (
            !this.haidao_idle ||
            !this.haidao_jingkong ||
            !this.haidao_liuhan ||
            !this.haidao_xuanyun
        )
            return
        this.haidao_jingkong.active = true
        this.haidao_idle.active =
            this.haidao_liuhan.active =
            this.haidao_xuanyun.active =
                false
    }

    private showHaidaoLiuhan() {
        if (
            !this.haidao_idle ||
            !this.haidao_jingkong ||
            !this.haidao_liuhan ||
            !this.haidao_xuanyun
        )
            return
        this.haidao_liuhan.active = true
        this.haidao_idle.active =
            this.haidao_jingkong.active =
            this.haidao_xuanyun.active =
                false
    }

    /**
     * 显示命中/未命中特效
     * 通过事件系统触发 UI 层显示 Spine 动画
     * 使用固定位置显示特效
     */
    private showHitMissSpine() {
        // 触发全局事件，不再传递位置信息
        cat.event.dispatchEvent(GameEventConstant.SHOW_HIT_MISS_EFFECT, {})

        console.log('[Pirate] 触发显示 hit_miss 特效事件，使用固定位置')
    }

    private showHaidaoXuanyun() {
        if (
            !this.haidao_idle ||
            !this.haidao_jingkong ||
            !this.haidao_liuhan ||
            !this.haidao_xuanyun
        )
            return
        this.haidao_xuanyun.active = true
        this.haidao_idle.active =
            this.haidao_jingkong.active =
            this.haidao_liuhan.active =
                false
    }

    private showHaidaoFly() {
        // 保存初始位置
        const initPos = this.node.position.clone()
        const distance = 0.4 // 飞行距离
        const angle = math.toRadian(75) // 75度角转换为弧度
        const duration = 0.8 // 总动画时长

        // 计算目标位置（右上方75度角）
        // const targetX = initPos.x + distance * Math.cos(angle)
        const targetY = initPos.y + distance * Math.sin(angle)

        // 创建抛物线动画序列
        tween(this.node)
            .sequence(
                // 上升阶段
                tween().to(
                    duration * 0.4,
                    {
                        position: v3(initPos.x, targetY, initPos.z),
                    },
                    {
                        easing: 'quadOut', // 使用二次曲线缓动使动画更自然
                    }
                )
                // // 下落阶段
                // tween().to(
                //     duration * 0.6,
                //     {
                //         position: v3(
                //             targetX + distance * 0.3,
                //             initPos.y - 0.1,
                //             initPos.z
                //         ),
                //     },
                //     {
                //         easing: 'quadIn', // 使用二次曲线缓动模拟重力
                //     }
                // ),
                // // 落地后旋转
                // tween().to(
                //     0.5, // 旋转动画持续时间
                //     {
                //         eulerAngles: v3(0, 0, -90), // 旋转90度使海盗横躺
                //     },
                //     {
                //         easing: 'bounceOut', // 使用弹性缓动效果
                //     }
                // )
            )
            .start()
    }

    /**抽牌广播 */
    private async onDrawBroadcastHandler(data: PlayerDrawnCardMessage) {
        cat.audio.playEffect(AudioEffectConstant.BEING_ATTRACT)
        this.showHaidaoJingkong()
        if (data.drawnCard === Card.DEATH) {
            cat.audio.playEffect(AudioEffectConstant.HIT_WEAKNESS)
            await sleep(300)
            this.showHaidaoXuanyun()
            await sleep(2000)
        } else {
            cat.audio.playEffect(AudioEffectConstant.ATTRACT)
            await sleep(300)
            this.showHaidaoLiuhan()
            // 显示未命中特效
            this.showHitMissSpine()
            await sleep(1000)
        }

        if (this.isGameOver) {
            return
        }

        this.showHaidaoIdle()
    }

    private async onDrawDeathBroadcastHandler(
        _data: PlayerDrawnDeathBroadcast
    ) {
        this.isGameOver = true
        await sleep(1000)
        this.showHaidaoFly()
    }
}
