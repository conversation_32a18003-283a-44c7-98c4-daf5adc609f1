[{"__type__": "cc.Prefab", "_name": "GameUIMain", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "GameUIMain", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 295}, {"__id__": 297}, {"__id__": 299}, {"__id__": 301}, {"__id__": 303}], "_prefab": {"__id__": 305}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "safe_area", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 11}], "_active": true, "_components": [{"__id__": 288}, {"__id__": 290}, {"__id__": 292}], "_prefab": {"__id__": 294}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "mask", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": false, "_components": [{"__id__": 4}, {"__id__": 6}, {"__id__": 8}], "_prefab": {"__id__": 10}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 524288, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 5}, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 3740}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bQ4GL2llEWo88j8yLeqN2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 7}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 84}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": true, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79mnWiBqhJcLjScstJozKi"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 9}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": -910, "_bottom": -910, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d6g3M72S9EFbwm6ZDPVAoG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c702enNIlGqLwJqnsm3y8I", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "main", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 12}, {"__id__": 144}], "_active": true, "_components": [{"__id__": 283}, {"__id__": 285}], "_prefab": {"__id__": 287}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "game_zone", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [{"__id__": 13}, {"__id__": 110}, {"__id__": 187}, {"__id__": 121}, {"__id__": 264}, {"__id__": 103}], "_active": true, "_components": [{"__id__": 278}, {"__id__": 280}], "_prefab": {"__id__": 282}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "desk", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [{"__id__": 14}, {"__id__": 38}, {"__id__": 50}, {"__id__": 59}, {"__id__": 71}, {"__id__": 81}, {"__id__": 87}, {"__id__": 91}], "_active": true, "_components": [{"__id__": 97}, {"__id__": 99}, {"__id__": 101}, {"__id__": 181}, {"__id__": 183}, {"__id__": 243}, {"__id__": 245}, {"__id__": 247}, {"__id__": 249}, {"__id__": 251}, {"__id__": 253}, {"__id__": 255}, {"__id__": 257}, {"__id__": 259}, {"__id__": 261}], "_prefab": {"__id__": 263}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "dashboard", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 13}, "_children": [{"__id__": 15}, {"__id__": 21}, {"__id__": 27}], "_active": true, "_components": [{"__id__": 33}, {"__id__": 35}], "_prefab": {"__id__": 37}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -104.695, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 14}, "_children": [], "_active": true, "_components": [{"__id__": 16}, {"__id__": 18}], "_prefab": {"__id__": 20}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 15}, "_enabled": true, "__prefab": {"__id__": 17}, "_contentSize": {"__type__": "cc.Size", "width": 435.765625, "height": 49.06}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "01HuysE0pE+5TpRdH13iv9"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 15}, "_enabled": true, "__prefab": {"__id__": 19}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "剩余        处，其中        处弱点", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 31, "_fontSize": 31, "_fontFamily": "Microsoft YaHei", "_lineHeight": 0, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 105, "g": 69, "b": 29, "a": 255}, "_outlineWidth": 5, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7axSlFkJJEmJ16DYgNQmPV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b3VpOq3mJEbZtIS/KjfkGY", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "remaining_count", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 14}, "_children": [], "_active": true, "_components": [{"__id__": 22}, {"__id__": 24}], "_prefab": {"__id__": 26}, "_lpos": {"__type__": "cc.Vec3", "x": -115.148, "y": 2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 21}, "_enabled": true, "__prefab": {"__id__": 23}, "_contentSize": {"__type__": "cc.Size", "width": 36.38916015625, "height": 66.7}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "62JZo+OodIOZU9YlCZ38m/"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 21}, "_enabled": true, "__prefab": {"__id__": 25}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 240, "b": 0, "a": 255}, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 45, "_fontSize": 45, "_fontFamily": "Microsoft YaHei", "_lineHeight": 0, "_overflow": 0, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 180, "g": 110, "b": 18, "a": 255}, "_outlineWidth": 5, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "47hvb0VrhJp6GIdhwErGzb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "85LGddQWxDnamPcDfB7dzr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "boom_count", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 14}, "_children": [], "_active": true, "_components": [{"__id__": 28}, {"__id__": 30}], "_prefab": {"__id__": 32}, "_lpos": {"__type__": "cc.Vec3", "x": 83.501, "y": 2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 27}, "_enabled": true, "__prefab": {"__id__": 29}, "_contentSize": {"__type__": "cc.Size", "width": 36.38916015625, "height": 66.7}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cfdlRfN2NNF5jrHOBd1bIO"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 27}, "_enabled": true, "__prefab": {"__id__": 31}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 240, "b": 0, "a": 255}, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 45, "_fontSize": 45, "_fontFamily": "Microsoft YaHei", "_lineHeight": 0, "_overflow": 0, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 180, "g": 110, "b": 18, "a": 255}, "_outlineWidth": 5, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "34cuaDshlK9YBWCtuhUa5t"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "31GuzpGJpN4JPqyzT7C05t", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 34}, "_contentSize": {"__type__": "cc.Size", "width": 575, "height": 78}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2d6/Tuhb1DN559Z6nXPHj2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 36}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "10dc9243-8297-4fa1-9720-b1576b07bd16@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b2r/1Dz4VL1JeiKkj6wUcJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "04dCTasO1IwIkewBI9nPtF", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "hand_card", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 13}, "_children": [{"__id__": 39}], "_active": true, "_components": [{"__id__": 44}, {"__id__": 46}, {"__id__": 48}], "_prefab": {"__id__": 58}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -488.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 38}, "_prefab": {"__id__": 40}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 39}, "asset": {"__uuid__": "5eb42090-d051-4180-b0a6-47b61a45c351", "__expectedType__": "cc.Prefab"}, "fileId": "cdE8mOophJm6wT0UhPe3Op", "instance": {"__id__": 41}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "e44T5AWatP8ZBiGdQPHYyD", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 42}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 43}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 80, "height": 235}}, {"__type__": "cc.TargetInfo", "localID": ["8aQaZa8hVGgZhhsRTLSmTa"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 38}, "_enabled": true, "__prefab": {"__id__": 45}, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 300}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9isIUTKpPMLQJhCBfdGFF"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 38}, "_enabled": true, "__prefab": {"__id__": 47}, "_alignFlags": 44, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 24, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5oX+WIntDPKo8fkPyW1Ce"}, {"__type__": "a2bd0mOsSNBvrnca+kgEpNN", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 38}, "_enabled": true, "__prefab": {"__id__": 49}, "list": {"__id__": 39}, "card_prefab": {"__uuid__": "928d32a7-db9c-461f-9716-6c45e92de7cd", "__expectedType__": "cc.Prefab"}, "is_steal_card": false, "player_desk_state_node": {"__id__": 50}, "player_desk_state_spriteFrame": [{"__uuid__": "ee4cd6f6-cb29-435a-b532-7f3896ff6c09@f9941", "__expectedType__": "cc.SpriteFrame"}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "25YgbtrkVMKLh2qpZAGyo9"}, {"__type__": "cc.Node", "_name": "player_desk_state", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 13}, "_children": [], "_active": true, "_components": [{"__id__": 51}, {"__id__": 53}, {"__id__": 55}], "_prefab": {"__id__": 57}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -486.578, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 52}, "_contentSize": {"__type__": "cc.Size", "width": 369, "height": 140}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7bDiBA1GVFroaoUHJP9oxb"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 54}, "_alignFlags": 0, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 196.00000000000006, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "00ODgT1dNEU55gnxCuDHuz"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 56}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c6VEUACThMTLg4x9UaV7ua"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "abSs9xOv1Oua5Pb8zIPQ1d", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "92APAbfKRIbaayky+BhPdz", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "draw_zone", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 13}, "_children": [{"__id__": 60}], "_active": true, "_components": [{"__id__": 68}], "_prefab": {"__id__": 70}, "_lpos": {"__type__": "cc.Vec3", "x": -146.063, "y": 150, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 59}, "_children": [], "_active": true, "_components": [{"__id__": 61}, {"__id__": 63}, {"__id__": 65}], "_prefab": {"__id__": 67}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -180, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 62}, "_contentSize": {"__type__": "cc.Size", "width": 251, "height": 346}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "32gTAC/YRG7rov6CEDMl7h"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 64}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9e9kC4UZpLfLLLnox+svJt"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 66}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d0cFXIstROPIS+zVw1nZmK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3fq/MwkAdF4bQT7MQSymro", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 59}, "_enabled": true, "__prefab": {"__id__": 69}, "_contentSize": {"__type__": "cc.Size", "width": 380, "height": 360}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "63uz3wBX5JqoGiuGfqc2SP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2bOJG2Ky9HH4wsLfl5ZExk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "draw_display_zone", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 13}, "_children": [{"__id__": 72}], "_active": true, "_components": [{"__id__": 78}], "_prefab": {"__id__": 80}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 614.069, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "shine", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 71}, "_children": [], "_active": false, "_components": [{"__id__": 73}, {"__id__": 75}], "_prefab": {"__id__": 77}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 131072, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 72}, "_enabled": true, "__prefab": {"__id__": 74}, "_contentSize": {"__type__": "cc.Size", "width": 609, "height": 607}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0091T6GNdK7IgX2tV+XEdc"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 72}, "_enabled": true, "__prefab": {"__id__": 76}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "7bfc07a3-5efe-415f-a84b-7c3e3e0294f1", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "idle", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "15421HofhF9ap6SFOMkIwz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6bJv3CIAZJtJX9a26VE2Cr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 71}, "_enabled": true, "__prefab": {"__id__": 79}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "105lA9xjFOGbr5OIoMg0nK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "71kw+9Cc5NZL54ELNzzL6o", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "play_zone", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 13}, "_children": [], "_active": true, "_components": [{"__id__": 82}, {"__id__": 84}], "_prefab": {"__id__": 86}, "_lpos": {"__type__": "cc.Vec3", "x": 312.059, "y": 103.243, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 81}, "_enabled": true, "__prefab": {"__id__": 83}, "_contentSize": {"__type__": "cc.Size", "width": 216, "height": 281}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b6SQH8EftN+LODgxYgfnvy"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 81}, "_enabled": true, "__prefab": {"__id__": 85}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c1765165-6b55-4bd3-a544-a4d32599a14b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84aj5Fo3ZMu6tEuCLfUneM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cacC5E64JGV6NcRslKaczj", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "card_effect", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 13}, "_children": [], "_active": true, "_components": [{"__id__": 88}], "_prefab": {"__id__": 90}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 87}, "_enabled": true, "__prefab": {"__id__": 89}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "87gCtLgVZBSrflzt4nv8b9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "baSKF0VzdDPYuLcMtOE5vk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "watch", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 13}, "_children": [], "_active": false, "_components": [{"__id__": 92}, {"__id__": 94}], "_prefab": {"__id__": 96}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -334, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 91}, "_enabled": true, "__prefab": {"__id__": 93}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 93}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e15TJ9ZBJEYYgTNKwi5uBr"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 91}, "_enabled": true, "__prefab": {"__id__": 95}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8b320af3-ada7-44d2-844c-8b634b27878d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aachoKRZ5H+KH9i7WCuO6F"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b5cLKFwfBE/Jy8/uw+mcsj", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 98}, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1325}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8f78x4RblAkY4Cg9tI3bSi"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 100}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 1261.983, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6ceiF/HCBO+p7J35gEh92T"}, {"__type__": "2a834HhEk5HP6lhCS3BmHku", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 102}, "card_move_tween": {"__id__": 103}, "draw_zone": {"__id__": 59}, "draw_display_zone": {"__id__": 71}, "players_node": {"__id__": 109}, "remaining_deck_cards": {"__id__": 24}, "boom_count": {"__id__": 30}, "play_zone": {"__id__": 81}, "hand_card": {"__id__": 48}, "hand_card_item_prefab": {"__uuid__": "928d32a7-db9c-461f-9716-6c45e92de7cd", "__expectedType__": "cc.Prefab"}, "players_prefab": {"__uuid__": "dfd15943-ebe3-42d7-9c7f-89485af0478a", "__expectedType__": "cc.Prefab"}, "action_node": {"__id__": 121}, "btn_action_draw": {"__id__": 122}, "btn_action_draw_spriteFrames": [{"__uuid__": "a4fac1c7-44fe-45e6-b7e7-c94d7faaadd1@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "2753d03e-4a95-49de-ab22-2497d406f739@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "bb3614d6-c64a-474d-b11a-1c4b70d443c3@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "9c3c33c4-c02e-4f4c-8416-3b38c835c9be@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "a1987934-b04c-4403-8a01-de3c073b6936@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "40bdbcc6-a4ad-48ad-8a14-564b8a2cf041@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "497dfc99-1e18-4c4f-a802-acb8301b4f03@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "d0247339-7ec7-421c-a301-37da11547bb3@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "66436ef5-e6f2-45db-b0a7-0d270379b445@f9941", "__expectedType__": "cc.SpriteFrame"}], "btn_action_play": {"__id__": 130}, "your_trun_node": {"__id__": 143}, "barrel": null, "camera3D": null, "ui_draw_prefab": {"__uuid__": "be4b906d-b7ab-43b3-aae8-e6cc9df55188", "__expectedType__": "cc.Prefab"}, "your_trun_spriteFrame": [{"__uuid__": "9bd7ab6c-4d80-4874-bbc4-e7c0cb937c40@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "9bd7ab6c-4d80-4874-bbc4-e7c0cb937c40@f9941", "__expectedType__": "cc.SpriteFrame"}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ab0acimsZAObN0fFUXyYvS"}, {"__type__": "cc.Node", "_name": "card_move_tween", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [], "_active": true, "_components": [{"__id__": 104}, {"__id__": 106}], "_prefab": {"__id__": 108}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 103}, "_enabled": true, "__prefab": {"__id__": 105}, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1325}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f5Kb9YmyxHF5Z8tzBBzIvB"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 103}, "_enabled": true, "__prefab": {"__id__": 107}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbioo4znhGx5kX7fWTzKhY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "89iJoLNLtPLahOUr0ph3yM", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "players", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 110}, "_children": [], "_active": true, "_components": [{"__id__": 116}, {"__id__": 118}], "_prefab": {"__id__": 120}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 50, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "player", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [{"__id__": 109}], "_active": true, "_components": [{"__id__": 111}, {"__id__": 113}], "_prefab": {"__id__": 115}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 110}, "_enabled": true, "__prefab": {"__id__": 112}, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1325}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6bIrndwZ1LbK0RdrmKSPFq"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 110}, "_enabled": true, "__prefab": {"__id__": 114}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "81zytwRfxOUqN2ja1I7Wyr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b017N9VFRNOaIfgPG2kZg0", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 109}, "_enabled": true, "__prefab": {"__id__": 117}, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1325}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e7sH/r8AhIRpwnoM0DJu56"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 109}, "_enabled": true, "__prefab": {"__id__": 119}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": -49.99999999999996, "_bottom": 49.99999999999996, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "efFvx4GyhHA59rH+0UTxAO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "31wXUAndFN/aRqxSa8R5Jb", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "action", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [{"__id__": 122}, {"__id__": 130}], "_active": true, "_components": [{"__id__": 138}, {"__id__": 140}], "_prefab": {"__id__": 142}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -369.48, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btn_action_draw", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 121}, "_children": [], "_active": true, "_components": [{"__id__": 123}, {"__id__": 125}, {"__id__": 127}], "_prefab": {"__id__": 129}, "_lpos": {"__type__": "cc.Vec3", "x": -161.55, "y": 130, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 122}, "_enabled": true, "__prefab": {"__id__": 124}, "_contentSize": {"__type__": "cc.Size", "width": 303, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5Pcs6QEdJ7J6cfwbz54Ln"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 122}, "_enabled": true, "__prefab": {"__id__": 126}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "a4fac1c7-44fe-45e6-b7e7-c94d7faaadd1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4faWMJvfpF8ae/X65nnn08"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 122}, "_enabled": true, "__prefab": {"__id__": 128}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aemujM7aNC+LFvklsgHssn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b1KflMpspEoYvbWoMxt9TS", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btn_action_play", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 121}, "_children": [], "_active": true, "_components": [{"__id__": 131}, {"__id__": 133}, {"__id__": 135}], "_prefab": {"__id__": 137}, "_lpos": {"__type__": "cc.Vec3", "x": 164.054, "y": 130, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 130}, "_enabled": true, "__prefab": {"__id__": 132}, "_contentSize": {"__type__": "cc.Size", "width": 303, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bbEcRD8KdOhZ7NYcsXiSZi"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 130}, "_enabled": true, "__prefab": {"__id__": 134}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3d622411-69cc-4aa0-bc15-e5cc645871b6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "29z24JeDdFcZqjDnhSI4La"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 130}, "_enabled": true, "__prefab": {"__id__": 136}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9aUlB6EVFP+qG6riC6JRt9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "33QAOZ0KFGO61bnmSYDKa5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 121}, "_enabled": true, "__prefab": {"__id__": 139}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "41KDSu+ExB37eANTTqqNiK"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 121}, "_enabled": true, "__prefab": {"__id__": 141}, "_alignFlags": 0, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 693, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "53c9Pv+4dPvbO4jEJ5+XJM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "28ONigs71N6rqcEJUO9+ny", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "your_trun", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 144}, "_children": [{"__id__": 166}, {"__id__": 172}], "_active": false, "_components": [{"__id__": 178}], "_prefab": {"__id__": 180}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 107.743, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 131072, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "interaction_animation", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [{"__id__": 143}, {"__id__": 145}, {"__id__": 153}], "_active": true, "_components": [{"__id__": 163}], "_prefab": {"__id__": 165}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "draw_mine", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 144}, "_children": [{"__id__": 146}], "_active": true, "_components": [{"__id__": 150}], "_prefab": {"__id__": 152}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "dangerous", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 145}, "_children": [], "_active": false, "_components": [{"__id__": 147}], "_prefab": {"__id__": 149}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 146}, "_enabled": true, "__prefab": {"__id__": 148}, "_contentSize": {"__type__": "cc.Size", "width": 608.8499755859375, "height": 685.3599853515625}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.4629547613037595, "y": 0.8461247181167089}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ecsJtaKUpERZ+AtsxbTbDO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c3sbcju6FMFaGFdUZPmNfg", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 145}, "_enabled": true, "__prefab": {"__id__": 151}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1eC/Aes5GVpnyWVAXPtV/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2cXfv5aP5I9ZYnkVUWc/sc", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "give_me_your_card_node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 144}, "_children": [{"__id__": 154}], "_active": true, "_components": [{"__id__": 160}], "_prefab": {"__id__": 162}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "give_me_your_card", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 153}, "_children": [], "_active": false, "_components": [{"__id__": 155}, {"__id__": 157}], "_prefab": {"__id__": 159}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 154}, "_enabled": true, "__prefab": {"__id__": 156}, "_contentSize": {"__type__": "cc.Size", "width": 377, "height": 469.2699890136719}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.18907161479919596, "y": 0.01954098981604976}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7f9xBX2z5NwqZTWmHsO36x"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 154}, "_enabled": true, "__prefab": {"__id__": 158}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "2d144563-6f77-4dac-8091-21c11f5b9191", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "idle", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bc2bvybPJGIqd8FwzIuTgj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cahrSWNSpCmp8sY8NQ4vXZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 153}, "_enabled": true, "__prefab": {"__id__": 161}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "64l/7c8zBH0oYEwRVH5411"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "05Y7W2s51Hrpu+5GV8RBkf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 144}, "_enabled": true, "__prefab": {"__id__": 164}, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1beCNnrXpPV51tpiy2nJSL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "beRXAXl+NIHolui/vz2+vf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 143}, "_children": [], "_active": true, "_components": [{"__id__": 167}, {"__id__": 169}], "_prefab": {"__id__": 171}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 131072, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 166}, "_enabled": true, "__prefab": {"__id__": 168}, "_contentSize": {"__type__": "cc.Size", "width": 675, "height": 182}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2aEmB15OVPY5WAPKlVGEtt"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 166}, "_enabled": true, "__prefab": {"__id__": 170}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "1a8fd920-fc44-45f8-90ea-f7b6bd84dbc8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3nxzg965EvYuUqO3+QFP4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8ekZz9xm9Ix4iXhuFqfwUg", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "confent", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 143}, "_children": [], "_active": true, "_components": [{"__id__": 173}, {"__id__": 175}], "_prefab": {"__id__": 177}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 131072, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 172}, "_enabled": true, "__prefab": {"__id__": 174}, "_contentSize": {"__type__": "cc.Size", "width": 583, "height": 148}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "12aaisxVZA/pywGw8SVG7K"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 172}, "_enabled": true, "__prefab": {"__id__": 176}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9bd7ab6c-4d80-4874-bbc4-e7c0cb937c40@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dc6cXPOBhCYoZJcuRdpl+M"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1ddWeMKPRG1qkhT6T5n2mf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 143}, "_enabled": true, "__prefab": {"__id__": 179}, "_contentSize": {"__type__": "cc.Size", "width": 583, "height": 148}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8Spb9KgRO0rZaAkZ+cBD8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a8pHtoZ6lKh6Nk8AlZhTOQ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "80275tZ/ANKf6xVlpv46z7s", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 182}, "shine": {"__id__": 72}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8fV1dcPIFD97AH92E7m4Ea"}, {"__type__": "ce694qleeBP3Jesy2WLe0UF", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 184}, "mask_node": {"__id__": 3}, "freeze_node": {"__id__": 185}, "defense_node": {"__id__": 197}, "peek_node": {"__id__": 191}, "strengthen_node": {"__id__": 203}, "weeken_node": {"__id__": 209}, "substitute_node": {"__id__": 215}, "revive_node": {"__id__": 221}, "swap_node": {"__id__": 227}, "death_node": {"__id__": 233}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a5W/2IRIhGmbSnTWGtS3KP"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 186}, "_enabled": true, "__prefab": {"__id__": 242}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "6bec4a70-52a6-4e69-9b51-508d555680e2", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": false, "_id": ""}, {"__type__": "cc.Node", "_name": "freeze", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 187}, "_children": [], "_active": false, "_components": [{"__id__": 239}, {"__id__": 185}], "_prefab": {"__id__": 241}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "card_effect", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [{"__id__": 186}, {"__id__": 188}, {"__id__": 194}, {"__id__": 200}, {"__id__": 206}, {"__id__": 212}, {"__id__": 218}, {"__id__": 224}, {"__id__": 230}], "_active": true, "_components": [{"__id__": 236}], "_prefab": {"__id__": 238}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 154.085, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "peek", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 187}, "_children": [], "_active": false, "_components": [{"__id__": 189}, {"__id__": 191}], "_prefab": {"__id__": 193}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 188}, "_enabled": true, "__prefab": {"__id__": 190}, "_contentSize": {"__type__": "cc.Size", "width": 512.6400146484375, "height": 534.5999755859375}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.4857209599035325, "y": 0.5342312367120658}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2dOpLT06pLXrYu4ciwSMqZ"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 188}, "_enabled": true, "__prefab": {"__id__": 192}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "f5543a37-06c9-4dc6-ae40-b5629d1c7e01", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e7rTIk5VlC7Kv2EF1MhIx+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bcPescSDhM8b1ujWOQFSAP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "defense", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 187}, "_children": [], "_active": false, "_components": [{"__id__": 195}, {"__id__": 197}], "_prefab": {"__id__": 199}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 194}, "_enabled": true, "__prefab": {"__id__": 196}, "_contentSize": {"__type__": "cc.Size", "width": 381.510009765625, "height": 437.3999938964844}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.4089276572393955, "y": 0.19599909591499118}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7fw2kg2gVCPIr0tEFmjpZc"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 194}, "_enabled": true, "__prefab": {"__id__": 198}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "e0aa4bae-504f-4ac5-83fa-931890abd54d", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3woKLQqtIBoRUxnFVhUeY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "78RfJ75YRBAIt6fmrLQnAz", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "strengthen", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 187}, "_children": [], "_active": false, "_components": [{"__id__": 201}, {"__id__": 203}], "_prefab": {"__id__": 205}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 200}, "_enabled": true, "__prefab": {"__id__": 202}, "_contentSize": {"__type__": "cc.Size", "width": 470.0299987792969, "height": 527.3800048828125}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5793885226039338, "y": 0.44872765691803135}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "deRI1s2AxAmIKbO1ehKTA8"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 200}, "_enabled": true, "__prefab": {"__id__": 204}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "b2a1ec79-0eb3-42c6-a0e6-7768302042c2", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0eBZEJBFBIUqdi0DeeIMFn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "34Z68SJuFNtbvR/iDSkvji", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "weaken", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 187}, "_children": [], "_active": false, "_components": [{"__id__": 207}, {"__id__": 209}], "_prefab": {"__id__": 211}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 206}, "_enabled": true, "__prefab": {"__id__": 208}, "_contentSize": {"__type__": "cc.Size", "width": 348.3599853515625, "height": 571.1400146484375}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5657079332040305, "y": 0.19016352877534928}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "64pZUgmZZPqa9fu/mRXrDK"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 206}, "_enabled": true, "__prefab": {"__id__": 210}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "fc652906-ed12-40e3-a634-9b06d0c69343", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "78+7e99R1MooCtzrzDHs5x"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "29JcOHmldIlIjJHSTwWePg", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "substitute", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 187}, "_children": [], "_active": false, "_components": [{"__id__": 213}, {"__id__": 215}], "_prefab": {"__id__": 217}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 212}, "_enabled": true, "__prefab": {"__id__": 214}, "_contentSize": {"__type__": "cc.Size", "width": 1531.969970703125, "height": 1237.1099853515625}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.826602335732131, "y": 0.21628635326888948}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9US7410RPEIEItAVs1pL6"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 212}, "_enabled": true, "__prefab": {"__id__": 216}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "990588a2-51a4-43be-932b-93089baf27ba", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5c7HV/KGBI26QARcoOB4Rl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7bFjNoPZFA6rxfbiigCe1o", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "revive", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 187}, "_children": [], "_active": false, "_components": [{"__id__": 219}, {"__id__": 221}], "_prefab": {"__id__": 223}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 218}, "_enabled": true, "__prefab": {"__id__": 220}, "_contentSize": {"__type__": "cc.Size", "width": 555.8800048828125, "height": 562.8499755859375}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.4259012699150967, "y": 0.4228657809908635}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aajp5SQtdHYKQsGjHNy4f/"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 218}, "_enabled": true, "__prefab": {"__id__": 222}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "150eb6d0-4b1b-4e10-bf3c-dbafb37581de", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ebQNCdYLFKRKY1OEju1bEu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "74kQKMtypIcppr9uoHPVRp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "swap", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 187}, "_children": [], "_active": false, "_components": [{"__id__": 225}, {"__id__": 227}], "_prefab": {"__id__": 229}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 224}, "_enabled": true, "__prefab": {"__id__": 226}, "_contentSize": {"__type__": "cc.Size", "width": 621.5999755859375, "height": 456.2099914550781}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5277027057545668, "y": 0.5722364754133178}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c7DX161xlNKpHm8Z7zkO8t"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 224}, "_enabled": true, "__prefab": {"__id__": 228}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "701f1974-829d-44e8-b497-23d974636a09", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "", "_premultipliedAlpha": false, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "46p1nuvJlH67jd/Rqj8Iw7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "28UMnyQg9A2KUaI1VfwCCM", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "death", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 187}, "_children": [], "_active": false, "_components": [{"__id__": 231}, {"__id__": 233}], "_prefab": {"__id__": 235}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -438.503, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.45, "y": 0.45, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 230}, "_enabled": true, "__prefab": {"__id__": 232}, "_contentSize": {"__type__": "cc.Size", "width": 1340, "height": 495.2200012207031}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5022238660214552, "y": 0.14809578804795775}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "900uzJnG9LC5MeTkAS+tHC"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 230}, "_enabled": true, "__prefab": {"__id__": 234}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": {"__uuid__": "e1bd85f1-f439-47cb-8842-c725fc56979a", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "<None>", "_premultipliedAlpha": false, "_timeScale": 0.5, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f3fsGc/KVBH7N2pyQ1HlCL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7ehD39uBhGOb2M3IV9A0Mf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 187}, "_enabled": true, "__prefab": {"__id__": 237}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73PVwAmtlLIKrXNnUwKmlx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "47mpQkpVtNbLaCN5jspjzb", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 186}, "_enabled": true, "__prefab": {"__id__": 240}, "_contentSize": {"__type__": "cc.Size", "width": 337.3599853515625, "height": 367.1499938964844}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.519889752801817, "y": 0.4788778707532543}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "49yQJwGRRHd5c4YzUyS4Ii"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e3PqgWaaZMIKT2SSk34faF", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.CompPrefabInfo", "fileId": "60+KCiEU1ChLECLkKQWEqb"}, {"__type__": "966afQ32EdI34eM+ipSuk69", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 244}, "choose_player_prefab": {"__uuid__": "06ab3072-d0d5-4d56-a309-7473a7621634", "__expectedType__": "cc.Prefab"}, "arrow_prefab": {"__uuid__": "c634dd78-24f0-4676-93e2-e5479d8a190c", "__expectedType__": "cc.Prefab"}, "passive_request_prefab": {"__uuid__": "c37e388a-f370-41cf-84df-26fdd98274a8", "__expectedType__": "cc.Prefab"}, "give_me_your_card_node": {"__id__": 153}, "give_me_your_card_skeleton": {"__id__": 157}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "335sBccGtC+4rxaST4Larj"}, {"__type__": "81fb4lmz/tHzZkprOBgy1fN", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 246}, "type": 0, "clip": null, "loop": false, "volume": 1, "playOnAwake": false, "active_remove_ui_prefab": {"__uuid__": "898c0c78-e9be-4e3d-9844-9a9ea21ed012", "__expectedType__": "cc.Prefab"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90/nGFjytLi5Pc3yk85IZw"}, {"__type__": "69ba922eitCtYeVjqYfzcA6", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 248}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "25cc/H3cpIX7vlpjkaqb6Y"}, {"__type__": "3f919t7f8VAyLK4SOWcWGLf", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 250}, "choose_player_prefab": {"__uuid__": "06ab3072-d0d5-4d56-a309-7473a7621634", "__expectedType__": "cc.Prefab"}, "arrow_prefab": {"__uuid__": "c634dd78-24f0-4676-93e2-e5479d8a190c", "__expectedType__": "cc.Prefab"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aeYX1HcyVM15dhHO9cvPoV"}, {"__type__": "47497kCf1JEq6WQpIIZ6Eoe", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 252}, "choose_player_prefab": {"__uuid__": "06ab3072-d0d5-4d56-a309-7473a7621634", "__expectedType__": "cc.Prefab"}, "arrow_prefab": {"__uuid__": "c634dd78-24f0-4676-93e2-e5479d8a190c", "__expectedType__": "cc.Prefab"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cfLs31SK1BBZ6jp7BCGVsg"}, {"__type__": "1fe03yNDMhBz48E2cVF/h1a", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 254}, "action_peek_ui_prefab": {"__uuid__": "7cff373e-aa0c-49aa-966c-a86000c60896", "__expectedType__": "cc.Prefab"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8cBmNXhmhHrYwRQj9bIZ/X"}, {"__type__": "9c4d6pUCRRJfLnFwF1Tvy67", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 256}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e3cV/P53VD+rC3v50sWmoM"}, {"__type__": "af807lXGLtLdIYuIRFuXJOA", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 258}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "09F8g1CvlP7q/jJMb6AZb2"}, {"__type__": "43861rpDd5Hk5FPQ0Fw9uUe", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 260}, "choose_player_prefab": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdZH0dLRxGWb/ii/D6BF+Y"}, {"__type__": "0b82bG35CFHtKHIIMG3NlVn", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 262}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bf5Ht+RUtAU5lOmUOwFEp/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "35kzdwd59ELa8s8ik312CW", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "card_info_node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [{"__id__": 265}], "_active": false, "_components": [{"__id__": 271}, {"__id__": 273}, {"__id__": 275}], "_prefab": {"__id__": 277}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -105.649, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 264}, "_children": [], "_active": true, "_components": [{"__id__": 266}, {"__id__": 268}], "_prefab": {"__id__": 270}, "_lpos": {"__type__": "cc.Vec3", "x": -2.1430000000000007, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 16384, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 265}, "_enabled": true, "__prefab": {"__id__": 267}, "_contentSize": {"__type__": "cc.Size", "width": 602.2860000000001, "height": 66.7}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6b3irwz/JIkaI7wxK2cSR0"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 265}, "_enabled": true, "__prefab": {"__id__": 269}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "Microsoft YaHei", "_lineHeight": 45, "_overflow": 3, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 82, "g": 50, "b": 18, "a": 255}, "_outlineWidth": 5, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "275j5gIExMdo3wq/5iWZpO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6abvparkVC+aqACbY1BLoT", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 264}, "_enabled": true, "__prefab": {"__id__": 272}, "_contentSize": {"__type__": "cc.Size", "width": 660, "height": 143}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bZDVjABBO/ZMIaCKRsbl9"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 264}, "_enabled": true, "__prefab": {"__id__": 274}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "015f6d86-2bab-4fee-9aa0-440aa25f2e79@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "692DTJFopNPLEMmp6N0vsz"}, {"__type__": "4db0bDWKVdMrae24w+aCYpX", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 264}, "_enabled": true, "__prefab": {"__id__": 276}, "card_info_label": {"__id__": 268}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cdy3Z/Gf1IuKdlxxFy2x/6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cbgfCXUC5NHp4xsCOs6MOn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 279}, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1325}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1cnCk2ZVKzqMZHBtnhQG6"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 281}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f2+wPNxO9Jg4eddDpc73QG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e77amUGaFHY7Z46zP2fokB", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 284}, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1325}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "80urFkuMdFgJnU5LFbElpQ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 286}, "_alignFlags": 40, "_target": null, "_left": 0, "_right": 0, "_top": 910, "_bottom": 910, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b35clt4jZGJqKpY1UwR3cj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7aoTLKeDRLJakXCUH77PX1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 289}, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d4yyrOv8BAWLtdboJFe+ZB"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 291}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1080, "_originalHeight": 1920, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ae6njuhJhBLJF0jPQyzHjb"}, {"__type__": "f2b7dlhPtBCi4X1Mb5KhCpy", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 293}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8aT66CmhNH7ZKE2Eu7CiSw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ebf36XyQBJfL92Nfye5PVp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 296}, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54wOo0DT1HSobZQ8U+NtbZ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 298}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "518SvYAmZBrrPuTDa1NWT9"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": false, "__prefab": {"__id__": 300}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 225, "g": 227, "b": 235, "a": 255}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "03SSFeWmJE3r1pLAddyNJ/"}, {"__type__": "75b505JGldL1oMS9RS2znUb", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 302}, "dashboard_node": {"__id__": 14}, "card_info_node": {"__id__": 264}, "desk": {"__id__": 101}, "watch_node": {"__id__": 91}, "barrel": null, "players_node": {"__id__": 110}, "hitMissEffect": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1ZPhqWmxN95CI7wwPjPc0"}, {"__type__": "b92fdurFYpBNb01k5SEPL9S", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 304}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ffKTwESSZPmLFXrg7+X15D"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "80wh8UaLNO3oPFwlRH61l0", "targetOverrides": [], "nestedPrefabInstanceRoots": [{"__id__": 39}]}]