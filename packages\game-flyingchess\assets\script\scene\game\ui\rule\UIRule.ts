import { _decorator, Component, Node } from 'cc'
import { cat } from '@/core/manager'
import { GameEventConstant } from '@/core/business/constant'
import UILayer from '@/core/manager/gui/layer/UILayer'

const { ccclass, property } = _decorator

@ccclass('UIRule')
export class UIRule extends UILayer {
    protected override onEventListener(): void {
        cat.event.on(
            GameEventConstant.CLOSE_RULE_UI,
            () => {
                cat.gui.closeUI(this)
            },
            this
        )
    }
}
