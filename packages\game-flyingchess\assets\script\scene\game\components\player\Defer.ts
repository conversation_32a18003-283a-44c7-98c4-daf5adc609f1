import {
    _decorator,
    Component,
    instantiate,
    isValid,
    Label,
    log,
    Node,
    Prefab,
    Animation,
    Skeleton,
    sp,
} from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { AudioEffectConstant } from '@/core/business/constant'
import { cat } from '@/core/manager'
import { create } from '@bufbuild/protobuf'
import store from '@/core/business/store'
import { Player, PlayerSchema } from '@/pb-generate/server/pirate/v1/player_pb'

import { PlayerItem } from './PlayerItem'

import { State as DeskState } from '@/pb-generate/server/pirate/v1/state_pb'

const { ccclass, property } = _decorator

@ccclass('Defer')
export class Defer extends BaseComponent {
    @property({ type: Node, tooltip: '延时特效动画节点' })
    spine_slot: Node

    @property({ type: sp.Skeleton })
    spine_defer: sp.Skeleton = null!

    override props = {
        player: create(PlayerSchema),
    }

    protected override initUI(): void {
        // this.spine_defer.node.active = false
    }

    protected override onLoad(): void {
        this.initDeferStatus()
    }

    protected override onAutoObserver(): void {
        this.addReaction(
            () => this.props.player.delayEffectCard,
            () => {
                this.updateDeferStatus()
            }
        )
    }

    override onDestroy(): void {}

    initDeferStatus() {
        if (this.props.player.delayEffectCard) {
            this.spine_defer.node.active = true
            this.spine_defer.setAnimation(0, 'defer_idle', false)
        } else {
            this.spine_defer.node.active = false
        }
    }

    /**更新延时状态 */
    updateDeferStatus() {
        if (this.props.player.delayEffectCard) {
            this.spine_defer.node.active = true
            this.spine_defer.setAnimation(0, 'defer_admission', false)
        } else {
            cat.audio.playEffect(AudioEffectConstant.EFFECT_TRIGGER)
            this.spine_defer.setAnimation(0, 'defer3_disappear', false)
            this.scheduleOnce(() => {
                this.spine_defer.node.active = false
            }, 2)
        }
    }

    hide() {
        this.node.active = false
    }
}
