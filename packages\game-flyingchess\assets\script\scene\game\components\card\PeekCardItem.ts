import {
    _decorator,
    Component,
    Input,
    Label,
    log,
    math,
    Node,
    Sprite,
    Sprite<PERSON>rame,
    Tween,
    tween,
    v3,
    Vec3,
} from 'cc'
import { CardItem } from './CardItem'
import { Card, CardState } from '@/pb-generate/server/pirate/v1/card_pb'
import { cat } from '@/core/manager'
import { GameEventConstant } from '@/core/business/constant'
import store from '@/core/business/store'
import {
    audioEffect,
    buttonLock,
    watchUser,
} from '@/core/business/hooks/Decorator'

const { ccclass, property } = _decorator

export type PeekCardItemProps = {
    cardState: CardState
}

@ccclass('PeekCardItem')
export class PeekCardItem extends CardItem<PeekCardItemProps> {
    @property({ type: [SpriteFrame], tooltip: '卡牌精灵图集' })
    card_spriteFrames: SpriteFrame[] = []

    override props: PeekCardItemProps = {
        cardState: CardState.UNSPECIFIED,
    }

    protected override onAutoObserver(): void {
        this.addAutorun([
            () => {
                this.card.getComponent(Sprite)!.spriteFrame =
                    this.card_spriteFrames[
                        this.props.cardState === CardState.BAD ? 0 : 1
                    ]
            },
        ])
    }
}
