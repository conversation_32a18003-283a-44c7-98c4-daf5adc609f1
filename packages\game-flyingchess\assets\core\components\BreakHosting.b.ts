import { _decorator, EventTouch, Node } from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { cat } from '@/core/manager'
import { GameEventConstant } from '@/core/business/constant'
import { reflect } from '@bufbuild/protobuf/reflect'
import {
    BreakHostingRequestSchema,
    BreakHostingResponseSchema,
    DataBroadcastRequestSchema,
    DataBroadcastResponseSchema,
} from '@/pb-generate/server/pirate/v1/handler_pb'
import { create } from '@bufbuild/protobuf'
import store from '../business/store'

const { ccclass, property } = _decorator

@ccclass('BreakHosting')
export class BreakHosting extends BaseComponent {
    protected override onLoad(): void {
        this.node.on(Node.EventType.TOUCH_START, this.onTouchStartHandler, this)
    }

    private onTouchStartHandler(event: EventTouch) {
        if (
            store.game.currentPlayer?.hosting
            // &&
            // store.game.isCurrentPlayerCursor
        ) {
            cat.ws.Request(
                'BreakHosting',
                reflect(
                    BreakHostingRequestSchema,
                    create(BreakHostingRequestSchema, {})
                ),
                BreakHostingResponseSchema
            )
        }
    }
}
