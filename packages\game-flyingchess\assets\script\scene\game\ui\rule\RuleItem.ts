import { _decorator, But<PERSON>, Component, Node } from 'cc'
import { BattleMode } from 'sgc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { cat } from '@/core/manager'
import { GameEventConstant } from '@/core/business/constant'
const { ccclass, property } = _decorator

@ccclass('RuleItem')
export class RuleItem extends BaseComponent {
    @property({ type: Button, tooltip: '关闭节点' })
    btn_close: But<PERSON>

    protected override onLoad(): void {
        this.btn_close.node.on(
            Button.EventType.CLICK,
            this.onCloseHandler,
            this
        )
    }

    private onCloseHandler() {
        cat.event.dispatchEvent(GameEventConstant.CLOSE_RULE_UI)
    }
}
