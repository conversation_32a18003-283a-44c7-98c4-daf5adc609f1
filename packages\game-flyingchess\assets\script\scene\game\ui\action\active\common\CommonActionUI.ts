import { _decorator, Component, log, Node } from 'cc'
import UILayer from '@/core/manager/gui/layer/UILayer'
import { cat } from '@/core/manager'
import { GameEventConstant } from '@/core/business/constant'
const { ccclass, property } = _decorator

/**
 * 界面(指定/被索取/侦查/探雷/拆除/埋雷)关闭的策略
 * 1.有其他界面弹窗的时候
 * 2.操作者变更的时候
 * 3.在操作者未变更的情况下 状态变更 比如
 *
 * ->侦查/探雷 有摸牌或出牌广播(√)
 * ->指定   可以在指定结束阶段发出关闭事件 (√)
 * ->拆除(√)/埋雷(√)/被索取(√) 可以在响应阶段发出关闭事件
 *
 * 拆除 -> 有拆除(主动和被动) 无拆除(出局和结算)
 * 被动拆除 ->POST(√)
 * 主动拆除 ->POST(√)
 * 无拆除(出局和结算)(√)
 * 被索取 -> 索取响应(√)
 */
@ccclass('CommonActionUI')
export class CommonActionUI<T extends object> extends UILayer<T> {
    protected override onEventListener(): void {
        cat.event.on(
            GameEventConstant.CLOSE_COMMON_UI,
            this.onCloseCommonUIHandler,
            this
        )
    }

    protected override start(): void {
        cat.event.dispatchEvent(GameEventConstant.CLOSE_TEAM_DROP_MINE)
    }

    protected onCloseCommonUIHandler() {
        this.close()
    }

    protected close() {
        cat.gui.closeUI(this, { isMotion: false })
    }
}
