一、名词解释
棋盘：游戏游玩的地图
棋子：玩家操作的对象，每个玩家四个棋子，最多四个玩家，每个玩家对应的颜色不同
格子：棋盘上棋子可以存在的位置，每个颜
色各 18 个格子，一共 72 个格子，棋子行进到不同的格子后会触发不同的效果（具体见后文）
起点：玩家棋子初始的位置
终点：玩家棋子需要到达的目标位置，棋子
骰子：游戏中确定玩家棋子行进距离的道具，骰子显示在棋盘的最中间，当轮到玩家交互的时候，会有特殊的样式，所有玩家都可以看见骰子被交互后的动效
迭子：有同色的两个或者两个以上的棋子，在一个格子内，则形成迭子
撞子：棋子行进的最后一格，如果有异色的棋子停留，且只有一个棋子，可以将该棋子逐回对应颜色的起点
跳子：棋子行进的最后一格，如果是和自己颜色相同的格子，可以跳至行进方向上下一个相同颜色的格子
飞棋：棋子行进的最后一格，如果是和自己颜色相同，且有一个飞机标识的格子，则会沿着虚线，飞到和自己颜色相同，带有箭头的格子中，其中：

二、游戏流程
流程图
图片: https://doc.yutang.work/uploader/f/GSNmHrTlEFjQUJTm.png?accessToken=eyJhbGciOiJIUzI1NiIsImtpZCI6ImRlZmF1bHQiLCJ0eXAiOiJKV1QifQ.eyJleHAiOjE3NDc5OTAzNDcsImZpbGVHVUlEIjoidlZxUlZlb1BaUkNHMFFxeSIsImlhdCI6MTc0Nzk4OTc0NywiaXNzIjoidXBsb2FkZXJfYWNjZXNzX3Jlc291cmNlIiwidXNlcklkIjo2NjN9.9m0nV5AemPhkX8ftPoin7FBRcKpfzsmVL3-AgheN7Pc
游戏准备阶段
1、分配座次：获取玩家数量（玩家数量限制见【场次规则】），分配玩家游戏座次（分配规则见【玩家座次分配规则】）；
2、按照玩家分配的座次，确定回合流转的顺序
--2.1 位号最靠前的用户为起始用户（有 1 号位用户则其为起始用户，如果没有，则位号最靠前的用户为起始用户）
--2.2 按照位号大小顺序，依次进行回合（位号从小到大排列，最大位号的用户结束回合后，重新回到位号最小的用户）
3、玩家自己的位号上，安排四个棋子，在起点等待操作
4、玩家显示位置
--4.1 玩家分配到指定颜色后，根据玩家所在的位置，转动棋盘，保证玩家的起点在左下角位置
--4.2 观战视角中，被观战的玩家，按照上述规则，在左下角位置
5、前端样式展示
图片: https://doc.yutang.work/uploader/f/oFMOW9JDjjt26nX3.png?accessToken=eyJhbGciOiJIUzI1NiIsImtpZCI6ImRlZmF1bHQiLCJ0eXAiOiJKV1QifQ.eyJleHAiOjE3NDc5OTAzNDcsImZpbGVHVUlEIjoidlZxUlZlb1BaUkNHMFFxeSIsImlhdCI6MTc0Nzk4OTc0NywiaXNzIjoidXBsb2FkZXJfYWNjZXNzX3Jlc291cmNlIiwidXNlcklkIjo2NjN9.9m0nV5AemPhkX8ftPoin7FBRcKpfzsmVL3-AgheN7Pc
游戏回合流程
1、进入回合的玩家，第一时间需要投骰子
2、根据投骰子的结果，执行棋子的操作，骰子结果和对应的操作内容，见后文【棋子移动规则】
3、操作完棋子后，根据棋子所在的格子的情况，执行特殊操作，特殊操作包括：
--3.1 格子自身携带的效果，具体效果见后卫【格子效果介绍】
--3.2 行进路线上或者格子中有其他棋子的情况，具体见后文【特殊判定】
4、一下情况回合结束：
--4.1 玩家投骰子后，操作完棋子，没有额外投骰子的机会时
--4.2 玩家投骰子后，根据骰子的数字，没有可以操作的棋子时
5、无可操作内容后，回合结束，回合结束时，判定是否达成胜利目标
--5.1 未达成，根据回合流转顺序，开启下个回合
--5.2 达成，则游戏结束，确定游戏最终排名
6、回合中效果样式
图片: https://doc.yutang.work/uploader/f/MlJIwzM1RP4qwOsk.png?accessToken=eyJhbGciOiJIUzI1NiIsImtpZCI6ImRlZmF1bHQiLCJ0eXAiOiJKV1QifQ.eyJleHAiOjE3NDc5OTAzNDcsImZpbGVHVUlEIjoidlZxUlZlb1BaUkNHMFFxeSIsImlhdCI6MTc0Nzk4OTc0NywiaXNzIjoidXBsb2FkZXJfYWNjZXNzX3Jlc291cmNlIiwidXNlcklkIjo2NjN9.9m0nV5AemPhkX8ftPoin7FBRcKpfzsmVL3-AgheN7Pc
游戏结束
1、游戏结束判定：
--1.1 某一个玩家，四个棋子都进入终点，游戏结束，改玩家为胜利者
--1.2 其他玩家的排名规则为（依次执行判定）：
----1.2.1 到达终点棋子越多，排名越高；
----1.2.2 到达终点的棋子相同时，已经起飞的棋子越多，排名越高；
----1.2.3 已经起飞的棋子相同时，棋子距离终点的格子数越少，排名越高；
----1.2.4 棋子距离终点的格子数相同时，被撞回起点的次数越多，排名越高；
----1.2.5 保底：麦位顺序越小，排名越高
2、游戏结束时，播放游戏结束的动效（有胜利和失败两种）
图片: https://doc.yutang.work/uploader/f/PXMUY4RC2lcpMyC0.png?accessToken=eyJhbGciOiJIUzI1NiIsImtpZCI6ImRlZmF1bHQiLCJ0eXAiOiJKV1QifQ.eyJleHAiOjE3NDc5OTAzNDcsImZpbGVHVUlEIjoidlZxUlZlb1BaUkNHMFFxeSIsImlhdCI6MTc0Nzk4OTc0NywiaXNzIjoidXBsb2FkZXJfYWNjZXNzX3Jlc291cmNlIiwidXNlcklkIjo2NjN9.9m0nV5AemPhkX8ftPoin7FBRcKpfzsmVL3-AgheN7Pc
四、游戏交互
棋盘内容
图片: https://doc.yutang.work/uploader/f/yxnUSopdqnpW4rvo.png?accessToken=eyJhbGciOiJIUzI1NiIsImtpZCI6ImRlZmF1bHQiLCJ0eXAiOiJKV1QifQ.eyJleHAiOjE3NDc5OTAzNDcsImZpbGVHVUlEIjoidlZxUlZlb1BaUkNHMFFxeSIsImlhdCI6MTc0Nzk4OTc0NywiaXNzIjoidXBsb2FkZXJfYWNjZXNzX3Jlc291cmNlIiwidXNlcklkIjo2NjN9.9m0nV5AemPhkX8ftPoin7FBRcKpfzsmVL3-AgheN7Pc
1、起点：
--1.1 分布在棋盘的四个角落，不同起点不同颜色
--1.2 起点中显示的内容包括
----1.2.1 用户头像：该起点如果有玩家，则会在起点的上方中部，显示用户头像和用户昵称
----1.2.2 玩家棋子：该起点如果有玩家，则会在起点的下方，显示玩家的棋子，棋子为四个，占据四个棋子位，当棋子从起点中起飞后，会留下空的棋子位
----1.2.3 回合状态：该起点如果有玩家，且轮到该玩家的回合，起点会播放回合进行汇总的动效
2、格子
--2.1 起始格子
----2.1.1 每个起点的逆时针方向，有一个单独的格子，为起始格子
----2.1.2 起飞，未行进的棋子，都会停留在起始格子
----2.1.3 除了改玩家起飞未行进的棋子以外，其他所有棋子都不会行进到其实格子
----2.1.4 起始格子出发的棋子，按照箭头，进入棋盘中常规格子中
--2.2 常规格子
----2.2.1 棋盘外围一圈，长方形和三角形的格子，为常规格子
----2.2.2 棋子在常规格子中，以顺时针方向，依次前进
--2.3 特殊格子
----2.3.1 在外圈常规格子中，有指向终点箭头的格子，为转向格子，进入指定颜色的转向格子之后，棋子会改变行进方向，向着终点前进
----2.3.2 在外圈常规格子中，有飞机的格子，为飞棋起点，进入指定颜色的飞起七点后，可以执行飞棋操作
--2.4 冲刺格子
----2.4.1 棋盘中央十字形状的，正方形格子，为冲刺格子
----2.4.2 棋子只能进入，和其颜色对应的冲刺格子中
----2.4.3 冲刺格子中，棋子不再是单向前进，会根据到达终点后是否剩余点数，相反方向前进（重新投骰子后，方向依旧是向终点）
3、骰子
--3.1 棋盘中央，被终点包围的位置，为骰子交互的位置
--3.2 轮到玩家的回合时，骰子交互位置会有动效，引导玩家点击
--3.3 点击骰子后，骰子会执行翻转的动效，确定最终的点数，改动效对所有玩家可见，最终点数也是对所有玩家展示
--3.4 有连投奖励的，骰子交互位置会重新有引导玩家点击的动效
4、终点
--4.1 冲刺格子末端，梯形的格子，为中点
--4.2 终点格子中，没有原型的占位，进入终点的棋子，按照长边为方向，居中依次排开
--4.3 进入终点格子的棋子，无法再交互
--4.4 终点格子进入四个棋子后，播放胜利动效，游戏结束
棋子移动规则
1、起飞：
--1.1 玩家回合中，投骰子投到 6，且起点有棋子，可以操作起点的棋子，进入开始区域，等待行进
--1.2 玩家回合中，投骰子投到 6，但是起点没有棋子时，不能执行起飞操作
2、行进：
--2.1 玩家回合中，投骰子投到任意点数，且有棋子在棋盘的格子中，则可以选择任意一个棋子按照骰子的点数，向前行进
--2.2 行进方向：
----2.2.1 棋子在外围一圈的格子中时，顺时针行进
----2.2.2 棋子遇到指定颜色的转向格子后，可以顺着箭头方向进入冲刺格子，方向向终点前进
----2.2.3 棋子在冲刺格子中行进，到了终点仍有剩余点数没有走完时，调转方向走完剩余点数
--2.3 跳子和飞棋：遇到跳子和飞棋的一般情况，正常执行跳子和飞棋，有特殊情况的，见【特殊判定】
3、连投奖励
--3.1 玩家回合，投骰子投到 6 点后，在完成棋子操作之后，可以再投一次
--3.2 （作为可选规则）玩家回合，完成一次撞子后，可以再投一次
--3.3 （作为可选规则）玩家回合，有棋子成功到达终点，可以再投一次
特殊判定
1、迭子时的判定
--1.1 棋子行进的最后一格，正好有其他颜色的棋子形成迭子，则行进的棋子，和形成迭子颜色的棋子中的两个棋子（无论迭子有几颗棋子形成，都只逐回两颗棋子），分别逐回对应颜色的起点
--1.2 棋子在行进过程中遇到迭子，则在到达迭子所在格子的前一格，调转方向走完剩余点数，最终终点格子的判定按照正常规则进行
--1.3 棋子是投到 6，并且经过（不是停留）迭子的格子时，棋子停留在迭子的格子上，并且执行连投奖励，在投完骰子之后，只能操作在迭子上的棋子，往前继续行进
2、飞棋时的判定
--2.1 飞棋和跳子的判定
----2.1.1 如果棋子直接行进到有飞机的格子中，则延虚线到达对岸后，可以再通过跳子，走到下一个同色格子中
----2.1.2 如果棋子时通过上一个同色格子，跳到有飞机的格子中，则延虚线到达对岸后，棋子就不再移动
--2.2 飞棋和迭子，撞子的判定
----2.2.1 飞棋的虚线上，如果有单颗的其他颜色的棋子，则完成撞子，执行飞棋的棋子正常按照飞棋的规则执行
----2.2.2 飞棋的虚线上，如果有其他颜色的迭子，则执行飞棋的棋子不能穿越虚线，需要绕行
五、游戏背景音乐和音效

五、其他判定
时间
用户特殊状态
1、逃跑
1）判定：用户主动行为，主动退出游戏或者房间，视为逃跑
2）标记：用户头像置灰，增加逃跑标记
3）操作：逃跑玩家执行托管操作
2、托管
1）进入托管：玩家在同一个回合，连续在两个倒计时阶段没有任何操作，头像上出现【托管】的字样
2）解除托管：在托管状态下，任何一个倒计时阶段有操作，则解除托管
3）托管过程中，玩家正常进行倒计时，倒计时结束，直接执行以下操作：
3、断线重连
1）断线期间，玩家不记录逃跑，但是托管状态正常判定
2）重连回来后，保留重连当时用户的状态（如果有托管状态，则需要手动触发解除）
3）重现回来后，如果是倒计时阶段，获取当前倒计时数值（不影响游戏整体流程）
4、观战
1）选定一个玩家作为主要观战视角
2）棋盘中间，增加【观战中】的标签提示
骰子点数策略
1、玩家不同场景下投骰子后，骰子点数的随机规则会有区别，不同场景下，骰子中随机数字的概率，是根据不同数字当前场景下的权重值进行随机的
2、具体场景下，骰子各个点数的权重值方案为：
3、单回合骰子次数
--3.1 在不添加可选玩法（到终点或者撞子成功后给连投奖励），玩家单回合投骰子次数最多 3 次
--3.2 添加可选玩法，玩家单回合投骰子次数根据实际情况确定
六、机器人
机器人行为直接套用托管的操作
